pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            url = uri("https://repo.seres.cn/nexus/repository/maven-JM3.0-SNAPSHOT/")
        }
        google()
        mavenCentral()
    }
}

rootProject.name = "OTAAndroid"
include ':nativelib'
include ':UDiskBackgroundUpgradeService'
include ':DdsTestApp'