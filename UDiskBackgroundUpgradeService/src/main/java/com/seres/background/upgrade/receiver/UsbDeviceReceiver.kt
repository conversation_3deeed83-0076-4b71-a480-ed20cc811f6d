package com.seres.background.upgrade.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import com.seres.background.upgrade.service.UsbDetectionService
import com.seres.background.upgrade.utils.LogUtils
import java.io.File

/**
 * USB设备插拔接收器
 * 监听USB设备的插入和拔出事件
 */
class UsbDeviceReceiver : BroadcastReceiver() {
    
    private val TAG = "UsbDeviceReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtils.i(TAG, "USB设备插入: ${it.deviceName}")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_USB_ATTACHED, it.deviceName)
                }
            }
            
            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                val device = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)
                device?.let {
                    LogUtils.i(TAG, "USB设备拔出: ${it.deviceName}")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_USB_DETACHED, it.deviceName)
                }
            }
            
            Intent.ACTION_MEDIA_MOUNTED -> {
                val path = intent.data?.path
                path?.let {
                    LogUtils.i(TAG, "存储设备挂载: $it")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_MEDIA_MOUNTED, it)
                }
            }
            
            Intent.ACTION_MEDIA_UNMOUNTED,
            Intent.ACTION_MEDIA_EJECT -> {
                val path = intent.data?.path
                path?.let {
                    LogUtils.i(TAG, "存储设备卸载: $it")
                    notifyUsbDetectionService(context, UsbDetectionService.ACTION_MEDIA_UNMOUNTED, it)
                }
            }
        }
    }
    
    /**
     * 通知USB检测服务
     */
    private fun notifyUsbDetectionService(context: Context, action: String, path: String) {
        try {
            val serviceIntent = Intent(context, UsbDetectionService::class.java).apply {
                this.action = action
                putExtra(UsbDetectionService.EXTRA_DEVICE_PATH, path)
            }

            // 尝试启动普通服务
            try {
                context.startService(serviceIntent)
                LogUtils.d(TAG, "成功通知USB检测服务: $action")
            } catch (e: Exception) {
                LogUtils.w(TAG, "启动服务失败，直接处理升级任务: ${e.message}")
                // 如果服务启动失败，直接处理升级任务
                handleUpgradeTaskDirectly(context, action, path)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "通知USB检测服务失败: ${e.message}")
            // 无论如何都要尝试直接处理升级任务
            handleUpgradeTaskDirectly(context, action, path)
        }
    }

    /**
     * 直接处理升级任务（当服务无法启动时）
     */
    private fun handleUpgradeTaskDirectly(context: Context, action: String, path: String) {
        try {
            LogUtils.i(TAG, "直接处理升级任务: $action - $path")

            // 只处理挂载事件
            if (action == UsbDetectionService.ACTION_MEDIA_MOUNTED) {
                val usbDir = File(path)
                if (usbDir.exists() && usbDir.isDirectory) {
                    val upgradeTaskFile = File(usbDir, "upgrade_task_info.json")
                    if (upgradeTaskFile.exists()) {
                        LogUtils.i(TAG, "直接发现升级任务文件: ${upgradeTaskFile.absolutePath}")

                        // 创建升级任务分析器并直接处理
                        val analyzer = com.seres.background.upgrade.analyzer.UpgradeTaskAnalyzer()
                        analyzer.setContext(context)
                        analyzer.analyzeUpgradeTask(usbDir)

                        LogUtils.i(TAG, "直接处理升级任务完成")
                    } else {
                        LogUtils.d(TAG, "USB设备中未发现升级任务文件: $path")
                    }
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "直接处理升级任务失败: ${e.message}")
        }
    }
}
