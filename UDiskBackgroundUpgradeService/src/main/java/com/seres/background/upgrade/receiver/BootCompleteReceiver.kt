package com.seres.background.upgrade.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.seres.background.upgrade.service.UsbDetectionService
import com.seres.background.upgrade.utils.LogUtils

/**
 * 开机启动接收器
 * 系统启动后自动启动后台升级服务
 */
class BootCompleteReceiver : BroadcastReceiver() {
    
    private val TAG = "BootCompleteReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_LOCKED_BOOT_COMPLETED -> {
                LogUtils.i(TAG, "系统启动完成，启动USB检测服务")

                try {
                    // 启动USB检测服务（普通后台服务）
                    val serviceIntent = Intent(context, UsbDetectionService::class.java)
                    context.startService(serviceIntent)

                    LogUtils.i(TAG, "USB检测服务启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动USB检测服务失败: ${e.message}")
                }
            }
        }
    }
}
