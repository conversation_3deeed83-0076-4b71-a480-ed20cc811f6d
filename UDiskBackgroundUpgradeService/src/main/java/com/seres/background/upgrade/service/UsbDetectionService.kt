package com.seres.background.upgrade.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.seres.background.upgrade.analyzer.UpgradeTaskAnalyzer
import com.seres.background.upgrade.manager.UpgradeTaskManager
import com.seres.background.upgrade.publisher.DdsService
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.util.concurrent.Executors

/**
 * USB检测服务
 * 监听USB设备插拔，检测upgrade_task_info.json文件
 */
class UsbDetectionService : Service(), DdsService.InventoryInfoCallback {

    private val TAG = "UsbDetectionService"
    private val threadPool = Executors.newCachedThreadPool()
    private val scheduledThreadPool = Executors.newScheduledThreadPool(2)
    private var upgradeTaskAnalyzer: UpgradeTaskAnalyzer? = null

    // 从BackgroundUpgradeService合并的组件
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var ddsService: DdsService? = null
    private var serviceStarted = false

    // 防止重复初始化的标志
    @Volatile
    private var componentsInitialized = false
    
    companion object {
        const val NOTIFICATION_ID = 2001
        const val CHANNEL_ID = "usb_detection_channel"

        // Action常量
        const val ACTION_USB_ATTACHED = "com.seres.background.upgrade.USB_ATTACHED"
        const val ACTION_USB_DETACHED = "com.seres.background.upgrade.USB_DETACHED"
        const val ACTION_MEDIA_MOUNTED = "com.seres.background.upgrade.MEDIA_MOUNTED"
        const val ACTION_MEDIA_UNMOUNTED = "com.seres.background.upgrade.MEDIA_UNMOUNTED"

        // Extra常量
        const val EXTRA_DEVICE_PATH = "device_path"

        // 配置文件名
        const val UPGRADE_TASK_FILE = "upgrade_task_info.json"
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UsbDetectionService onCreate")

        // 创建通知渠道（但不创建常驻通知）
        createNotificationChannel()

        // 初始化组件
        initializeComponents()

        upgradeTaskAnalyzer = UpgradeTaskAnalyzer()
        upgradeTaskAnalyzer?.setContext(this)

        // 检查已挂载的USB设备
        checkExistingUsbDevices()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "UsbDetectionService onStartCommand: ${intent?.action} - flags: $flags, startId: $startId")

        // 重新初始化组件（防止重启后组件丢失）
        if (!componentsInitialized || upgradeTaskManager == null || ddsService == null) {
            LogUtils.i(TAG, "检测到组件丢失，重新初始化")
            initializeComponents()
        }

        if (upgradeTaskAnalyzer == null ) {
            LogUtils.i(TAG, "检测到USB服务组件丢失，重新初始化")
            upgradeTaskAnalyzer = UpgradeTaskAnalyzer()
            upgradeTaskAnalyzer?.setContext(this)
        }

        intent?.let { handleIntent(it) }

        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UsbDetectionService onDestroy - 尝试重启服务")

        // 在服务被销毁时尝试重启
        try {
            val restartIntent = Intent(this, UsbDetectionService::class.java)
            restartIntent.action = "com.seres.background.upgrade.RESTART_SERVICE"

            // 延迟重启，避免立即重启被系统阻止
            val pendingIntent = android.app.PendingIntent.getService(
                this,
                2001,
                restartIntent,
                android.app.PendingIntent.FLAG_ONE_SHOT or android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = getSystemService(android.content.Context.ALARM_SERVICE) as android.app.AlarmManager
            alarmManager.setExact(
                android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                android.os.SystemClock.elapsedRealtime() + 5000, // 5秒后重启
                pendingIntent
            )

            LogUtils.i(TAG, "已设置服务重启定时器")
        } catch (e: Exception) {
            LogUtils.e(TAG, "设置服务重启失败: ${e.message}")
        }

        // 清理资源
        cleanup()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        LogUtils.i(TAG, "UsbDetectionService onTaskRemoved - 任务被移除")

        // 任务被移除时也尝试重启
        try {
            val restartIntent = Intent(this, UsbDetectionService::class.java)
            restartIntent.action = "com.seres.background.upgrade.RESTART_SERVICE"
            startService(restartIntent)
            LogUtils.i(TAG, "任务移除后重启服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "任务移除后重启服务失败: ${e.message}")
        }
    }
    
    /**
     * 处理Intent
     */
    private fun handleIntent(intent: Intent) {
        val action = intent.action ?: return
        val devicePath = intent.getStringExtra(EXTRA_DEVICE_PATH) ?: return
        
        when (action) {
            ACTION_USB_ATTACHED,
            ACTION_MEDIA_MOUNTED -> {
                LogUtils.i(TAG, "处理USB设备挂载: $devicePath")
                handleUsbMounted(devicePath)
            }

            ACTION_USB_DETACHED,
            ACTION_MEDIA_UNMOUNTED -> {
                LogUtils.i(TAG, "处理USB设备卸载: $devicePath")
                handleUsbUnmounted(devicePath)
            }
        }
    }
    
    /**
     * 处理USB设备挂载
     */
    private fun handleUsbMounted(path: String) {
        threadPool.submit {
            try {
                val usbDir = File("$path/")
                if (usbDir.exists() && usbDir.isDirectory) {
                    // 显示USB检测通知
                    showUsbDetectionNotification("检测到USB设备: ${File(path).name}")

                    // 检查是否包含upgrade_task_info.json文件
                    val upgradeTaskFile = File(usbDir, UPGRADE_TASK_FILE)
                    if (upgradeTaskFile.exists()) {
                        LogUtils.i(TAG, "发现upgrade_task_info.json文件: ${upgradeTaskFile.absolutePath}")
                        showUpgradeTaskNotification("发现升级任务配置文件")
                        // 分析升级任务
                        upgradeTaskAnalyzer?.analyzeUpgradeTask(usbDir)
                    } else {
                        LogUtils.d(TAG, "USB设备中未发现upgrade_task_info.json文件: $path")
                        // 延迟取消通知（给用户时间看到USB检测通知）
                        scheduledThreadPool.schedule({
                            cancelNotification()
                        }, 3, java.util.concurrent.TimeUnit.SECONDS)
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "处理USB挂载事件失败: ${e.message}")
                showUsbDetectionNotification("处理USB设备时出错")
            }
        }
    }
    
    /**
     * 处理USB设备卸载
     */
    private fun handleUsbUnmounted(path: String) {
        LogUtils.i(TAG, "USB设备已卸载: $path")
        showUsbDetectionNotification("USB设备已移除: ${File(path).name}")

        // 取消相关的升级任务
        upgradeTaskAnalyzer?.cancelTasksForUsbPath(path)

        // 延迟取消通知
        scheduledThreadPool.schedule({
            cancelNotification()
        }, 3, java.util.concurrent.TimeUnit.SECONDS)
    }
     
    /**
     * 检查已挂载的USB设备
     */
    private fun checkExistingUsbDevices() {
        threadPool.submit {
            try {
                // 检查常见的USB挂载点
                val mountPoints = arrayOf(
                    "/mnt/usb",
                    "/mnt/usbdisk",
                    "/storage/usb",
                    "/mnt/media_rw"
                )
                
                for (mountPoint in mountPoints) {
                    val dir = File(mountPoint)
                    if (dir.exists() && dir.isDirectory) {
                        dir.listFiles()?.forEach { subDir ->
                            if (subDir.isDirectory) {
                                val upgradeTaskFile = File(subDir, UPGRADE_TASK_FILE)
                                if (upgradeTaskFile.exists()) {
                                    LogUtils.i(TAG, "启动时发现已挂载的升级任务: ${subDir.absolutePath}")
                                    handleUsbMounted(subDir.absolutePath)
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "检查已挂载USB设备失败: ${e.message}")
            }
        }
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        synchronized(this) {
            if (componentsInitialized) {
                LogUtils.i(TAG, "组件已初始化，跳过重复初始化")
                return
            }

            try {
                // 初始化升级任务管理器
                upgradeTaskManager = UpgradeTaskManager.getInstance(this)

                // 初始化DDS服务 (使用单例)
                ddsService = DdsService.getInstance().apply {
                    setInventoryInfoCallback(this@UsbDetectionService)
                    if (!initialize()) {
                        LogUtils.e(TAG, "DDS服务初始化失败")
                    }
                }

                // 设置升级任务管理器的DDS服务
                upgradeTaskManager?.setDdsService(ddsService!!)
                serviceStarted = true
                componentsInitialized = true
                LogUtils.i(TAG, "组件初始化完成")

            } catch (e: Exception) {
                LogUtils.e(TAG, "组件初始化失败: ${e.message}")
            }
        }
    }

    /**
     * DDS资产信息回调实现
     */
    override fun onInventoryInfoReceived(inventoryInfoStatus: String) {
        try {
            LogUtils.d(TAG, "收到资产信息通知: $inventoryInfoStatus")

            // 解析资产信息并进行版本兼容性检查
            // 这里需要根据实际的数据格式来解析
            // 暂时使用简化的处理逻辑

            LogUtils.i(TAG, "资产信息处理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理资产信息时出错: ${e.message}")
        }
    }

    /**
     * 清理资源 (从BackgroundUpgradeService合并)
     */
    private fun cleanup() {
        synchronized(this) {
            try {
                // 清理DDS服务
                ddsService?.cleanup()

                // 关闭线程池
                threadPool.shutdown()
                scheduledThreadPool.shutdown()

                // 取消所有通知
                cancelNotification()

                // 重置初始化标志
                componentsInitialized = false

                LogUtils.i(TAG, "资源清理完成")
            } catch (e: Exception) {
                LogUtils.e(TAG, "清理资源时出错: ${e.message}")
            }
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "USB检测服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "监听USB设备插拔和升级任务"
            setShowBadge(false)
        }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }



    /**
     * 显示USB检测通知
     */
    private fun showUsbDetectionNotification(content: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("USB设备检测")
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(false)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
        LogUtils.i(TAG, "显示USB检测通知: $content")
    }

    /**
     * 显示升级任务通知
     */
    private fun showUpgradeTaskNotification(content: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("升级任务")
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(false)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
        LogUtils.i(TAG, "显示升级任务通知: $content")
    }

    /**
     * 取消通知
     */
    private fun cancelNotification() {
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.cancel(NOTIFICATION_ID)
        LogUtils.i(TAG, "已取消通知")
    }
}
