package com.seres.background.upgrade.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志工具类
 * 支持控制台输出和文件记录
 */
object LogUtils {
    
    private const val TAG_PREFIX = "BackgroundUpgrade-"

    /**
     * Debug级别日志
     */
    fun d(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.d(fullTag, message)
    }
    
    /**
     * Info级别日志
     */
    fun i(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.i(fullTag, message)
    }
    
    /**
     * Warning级别日志
     */
    fun w(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.w(fullTag, message)
    }
    
    /**
     * Error级别日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        val fullTag = TAG_PREFIX + tag
        if (throwable != null) {
            Log.e(fullTag, message, throwable)
        } else {
            Log.e(fullTag, message)
        }
    }

}
