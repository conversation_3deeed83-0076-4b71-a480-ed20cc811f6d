package com.seres.background.upgrade.analyzer

import android.content.Context
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.manager.UpgradeTaskManager
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest

/**
 * 升级任务分析器
 * 负责解析upgrade_task_info.json文件并处理升级任务
 */
class UpgradeTaskAnalyzer {

    private val TAG = "UpgradeTaskAnalyzer"
    private val gson = Gson()
    private var upgradeTaskManager: UpgradeTaskManager? = null

    private var context: Context? = null
    private var notificationCallback: NotificationCallback? = null

    /**
     * 通知回调接口
     */
    interface NotificationCallback {
        fun onUpgradeTaskStarted(message: String)
        fun onUpgradeTaskProgress(message: String)
        fun onUpgradeTaskCompleted(message: String)
        fun onUpgradeTaskFailed(message: String)
    }

    
    /**
     * 设置上下文
     */
    fun setContext(context: Context) {
        this.context = context
    }

    /**
     * 设置通知回调
     */
    fun setNotificationCallback(callback: NotificationCallback) {
        this.notificationCallback = callback
    }
    
    /**
     * 分析升级任务
     */
    fun analyzeUpgradeTask(usbDir: File) {
        LogUtils.i(TAG, "开始分析升级任务: ${usbDir.absolutePath}")
        
        try {
            // 1. 解析upgrade_task_info.json文件
            val upgradeTaskFile = File(usbDir, "upgrade_task_info.json")
            val upgradeTaskInfo = parseUpgradeTaskInfo(upgradeTaskFile)
            
            if (upgradeTaskInfo == null) {
                LogUtils.e(TAG, "解析upgrade_task_info.json失败")
                return
            }
            
            // 2. 获取升级包文件列表
            val dcList = upgradeTaskInfo.dpPackage.pkgInfo.dcList
            
            if (dcList.isEmpty()) {
                LogUtils.w(TAG, "upgrade_task_info.json中未找到升级包信息")
                return
            }
            
            LogUtils.i(TAG, "发现 ${dcList.size} 个升级包，开始处理")
            
            // 3. 扫描并验证升级包文件
            val validPackages = validateUpgradePackages(usbDir, dcList)
            
            if (validPackages.isEmpty()) {
                LogUtils.w(TAG, "未找到有效的升级包文件")
                return
            }
            
            // 处理升级任务
            val taskManager = getUpgradeTaskManager()
            if (taskManager != null) {
                LogUtils.i(TAG, "开始处理升级任务，任务管理器已就绪")
                notificationCallback?.onUpgradeTaskStarted("开始处理升级任务，包含 ${validPackages.size} 个升级包")

                // 设置任务管理器的通知回调
                taskManager.setNotificationCallback(object : UpgradeTaskManager.NotificationCallback {
                    override fun onTaskProgress(message: String) {
                        notificationCallback?.onUpgradeTaskProgress(message)
                    }

                    override fun onTaskCompleted(message: String) {
                        notificationCallback?.onUpgradeTaskCompleted(message)
                    }

                    override fun onTaskFailed(message: String) {
                        notificationCallback?.onUpgradeTaskFailed(message)
                    }
                })

                taskManager.processUpgradeTaskInfo(upgradeTaskInfo, usbDir, validPackages)
                LogUtils.i(TAG, "升级任务已提交给任务管理器")
            } else {
                LogUtils.e(TAG, "无法获取升级任务管理器，升级任务处理失败")
                notificationCallback?.onUpgradeTaskFailed("无法获取升级任务管理器")
            }

            LogUtils.i(TAG, "升级任务分析完成，包含 ${validPackages.size} 个有效升级包")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "分析升级任务失败: ${e.message}", e)
        }
    }
    
    /**
     * 解析upgrade_task_info.json配置文件
     */
    private fun parseUpgradeTaskInfo(configFile: File): UpgradeTaskInfo? {
        return try {
            if (!configFile.exists()) {
                LogUtils.e(TAG, "upgrade_task_info.json文件不存在: ${configFile.absolutePath}")
                return null
            }
            
            val configJson = configFile.readText()
            LogUtils.d(TAG, "读取到upgrade_task_info.json内容长度: ${configJson.length}")
            
            val upgradeTaskInfo = gson.fromJson(configJson, UpgradeTaskInfo::class.java)
            LogUtils.i(TAG, "成功解析upgrade_task_info.json")
            LogUtils.d(TAG, "包ID: ${upgradeTaskInfo.dpPackage.pkgId}")
            LogUtils.d(TAG, "版本: ${upgradeTaskInfo.dpPackage.pkgInfo.pkgVer}")
            LogUtils.d(TAG, "升级包数量: ${upgradeTaskInfo.dpPackage.pkgInfo.dcList.size}")
            
            upgradeTaskInfo
            
        } catch (e: JsonSyntaxException) {
            LogUtils.e(TAG, "upgrade_task_info.json格式错误: ${e.message}")
            null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取upgrade_task_info.json失败: ${e.message}")
            null
        }
    }
    
    /**
     * 验证升级包文件
     */
    private fun validateUpgradePackages(usbDir: File, dcList: List<DcItem>): List<DcItem> {
        val validPackages = mutableListOf<DcItem>()
        
        // 获取USB目录下的所有文件
        val allFiles = getAllFiles(usbDir)
        LogUtils.d(TAG, "USB目录下找到 ${allFiles.size} 个文件")
        
        // 验证每个升级包
        for (dcItem in dcList) {
            val packageFileName = extractFileNameFromPath(dcItem.path)
            val packageFile = findFileInDirectory(allFiles, packageFileName)
            
            if (packageFile != null && packageFile.exists()) {
                // 验证文件大小
                val actualSize = packageFile.length()
                val expectedSize = dcItem.zipSizeInByte
                
                if (expectedSize > 0 && actualSize != expectedSize) {
                    LogUtils.w(TAG, "升级包文件大小不匹配: $packageFileName, 期望: $expectedSize, 实际: $actualSize")
                }
                
                validPackages.add(dcItem)
                LogUtils.d(TAG, "找到有效升级包: $packageFileName -> ${packageFile.name}")
            } else {
                LogUtils.w(TAG, "未找到升级包文件: $packageFileName")
            }
        }
        
        return validPackages
    }
    
    /**
     * 从路径中提取文件名
     */
    private fun extractFileNameFromPath(path: String): String {
        return if (path.startsWith("http")) {
            // 如果是URL，提取最后的文件名部分
            path.substringAfterLast("/")
        } else {
            // 如果是本地路径，直接提取文件名
            File(path).name
        }
    }
    
    /**
     * 获取目录下所有文件
     */
    private fun getAllFiles(dir: File): List<File> {
        val files = mutableListOf<File>()
        
        try {
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(file)
                } else if (file.isDirectory) {
                    files.addAll(getAllFiles(file))
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "扫描目录失败: ${dir.absolutePath} - ${e.message}")
        }
        
        return files
    }
    
    /**
     * 在文件列表中查找指定文件
     */
    private fun findFileInDirectory(files: List<File>, fileName: String): File? {
        return files.find { file ->
            file.name.equals(fileName, ignoreCase = true) ||
            file.name.contains(fileName, ignoreCase = true)
        }
    }
    
    /**
     * 取消USB路径相关的任务
     */
    fun cancelTasksForUsbPath(usbPath: String) {
        LogUtils.i(TAG, "取消USB路径相关的任务: $usbPath")
        getUpgradeTaskManager()?.cancelTasksByUsbPath(usbPath)
    }
    
    /**
     * 获取升级任务管理器实例
     */
    private fun getUpgradeTaskManager(): UpgradeTaskManager? {
        if (upgradeTaskManager == null) {
            context?.let { ctx ->
                upgradeTaskManager = UpgradeTaskManager.getInstance(ctx)
                LogUtils.d(TAG, "UpgradeTaskManager已初始化")
            } ?: run {
                LogUtils.e(TAG, "Context为null，无法初始化UpgradeTaskManager")
                return null
            }
        }
        return upgradeTaskManager
    }
}
