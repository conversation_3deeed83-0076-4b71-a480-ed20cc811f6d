@echo off
chcp 65001 >nul
echo === Starting Background Upgrade Service ===
echo.

echo 1. Force stop existing service...
adb shell am force-stop com.seres.background.upgrade

echo 2. Grant necessary permissions...
adb shell pm grant com.seres.background.upgrade android.permission.WRITE_EXTERNAL_STORAGE
adb shell pm grant com.seres.background.upgrade android.permission.READ_EXTERNAL_STORAGE
echo    Note: Some permissions like MANAGE_EXTERNAL_STORAGE require manual setup

echo 3. Disable battery optimization...
adb shell dumpsys deviceidle whitelist +com.seres.background.upgrade

echo 4. Start background service...
adb shell am startservice com.seres.background.upgrade/.service.UsbDetectionService

echo 5. Start watchdog service...
adb shell am start-foreground-service com.seres.background.upgrade/.service.WatchdogService

echo 6. Wait 3 seconds...
timeout /t 3 >nul

echo 7. Check if service is running...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [SUCCESS] Service is now running!
) else (
    echo    [ERROR] Service failed to start
    echo    Trying alternative method...
    adb shell am broadcast -a android.intent.action.BOOT_COMPLETED
    timeout /t 2 >nul
    adb shell ps | findstr background.upgrade
    if %errorlevel% equ 0 (
        echo    [SUCCESS] Service started via boot broadcast!
    ) else (
        echo    [ERROR] All start methods failed
    )
)

echo.
echo === Service Status ===
adb shell dumpsys activity services | findstr BackgroundUpgrade

echo.
echo === Recent Logs ===
adb logcat -d | findstr BackgroundUpgrade | more +1

echo.
echo Done! Use check_service.bat to verify status.
pause
