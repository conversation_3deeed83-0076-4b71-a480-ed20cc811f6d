#!/bin/bash

# 构建和测试脚本
# 使用方法: ./build_and_test.sh

echo "=== U盘升级服务构建和测试 ==="
echo "时间: $(date)"
echo ""

# 检查ADB连接
if ! adb devices | grep -q "device$"; then
    echo "❌ 错误: 未检测到ADB设备连接"
    echo "请确保设备已连接并启用USB调试"
    exit 1
fi

echo "✅ ADB设备连接正常"
echo ""

# 1. 构建应用
echo "1. 构建应用:"
echo "   📋 开始构建..."

# 检查是否在项目根目录
if [ ! -f "build.gradle" ]; then
    echo "   ❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 构建debug版本
if ./gradlew assembleDebug; then
    echo "   ✅ 构建成功"
else
    echo "   ❌ 构建失败"
    exit 1
fi
echo ""

# 2. 卸载旧版本
echo "2. 卸载旧版本:"
if adb shell pm list packages | grep -q com.seres.background.upgrade; then
    echo "   📋 卸载旧版本..."
    adb uninstall com.seres.background.upgrade
    echo "   ✅ 旧版本已卸载"
else
    echo "   ℹ️  未发现旧版本"
fi
echo ""

# 3. 安装新版本
echo "3. 安装新版本:"
apk_path="build/outputs/apk/debug/UDiskBackgroundUpgradeService-debug.apk"
if [ -f "$apk_path" ]; then
    echo "   📋 安装新版本..."
    if adb install "$apk_path"; then
        echo "   ✅ 安装成功"
    else
        echo "   ❌ 安装失败"
        exit 1
    fi
else
    echo "   ❌ 错误: 找不到APK文件: $apk_path"
    exit 1
fi
echo ""

# 4. 启动服务
echo "4. 启动服务:"
echo "   📋 启动USB检测服务..."
adb shell am startservice com.seres.background.upgrade/.service.UsbDetectionService
sleep 3

if adb shell ps | grep -q com.seres.background.upgrade; then
    echo "   ✅ 服务启动成功"
else
    echo "   ❌ 服务启动失败"
    exit 1
fi
echo ""

# 5. 运行基本验证
echo "5. 运行基本验证:"
if [ -f "check_service.sh" ]; then
    echo "   📋 运行服务验证脚本..."
    chmod +x check_service.sh
    ./check_service.sh
else
    echo "   ⚠️  未找到验证脚本，跳过"
fi
echo ""

# 6. 运行通知逻辑测试
echo "6. 运行通知逻辑测试:"
if [ -f "test_notification_logic.sh" ]; then
    echo "   📋 运行通知逻辑测试..."
    chmod +x test_notification_logic.sh
    ./test_notification_logic.sh
else
    echo "   ⚠️  未找到通知测试脚本，跳过"
fi
echo ""

# 7. 总结
echo "=== 构建和测试总结 ==="
echo "🎉 构建和基本测试完成!"
echo ""
echo "📋 下一步建议:"
echo "   1. 插入真实USB设备测试检测功能"
echo "   2. 在USB设备中放置upgrade_task_info.json文件测试升级逻辑"
echo "   3. 观察通知行为是否符合预期"
echo "   4. 检查日志输出: adb logcat | grep 'UsbDetectionService'"
echo ""
echo "完成时间: $(date)"
