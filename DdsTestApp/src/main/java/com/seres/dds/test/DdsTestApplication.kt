package com.seres.dds.test

import android.app.Application
import android.content.Intent
import com.seres.dds.test.service.DdsTestService
import com.seres.dds.test.utils.LogUtils

/**
 * DDS测试应用程序类
 */
class DdsTestApplication : Application() {

    private val TAG = "DdsTestApplication"

    override fun onCreate() {
        super.onCreate()
        LogUtils.d(TAG, "DdsTestApplication onCreate")

        // 启动DDS测试服务
        startDdsTestService()
    }

    /**
     * 启动DDS测试服务
     */
    private fun startDdsTestService() {
        try {
            val intent = Intent(this, DdsTestService::class.java)
            intent.action = "com.seres.dds.test.START_SERVICE"

            // 尝试启动前台服务
            try {
                startForegroundService(intent)
                LogUtils.d(TAG, "DDS测试服务启动成功 (前台服务)")
            } catch (e: Exception) {
                // 如果前台服务启动失败，尝试普通服务
                LogUtils.w(TAG, "前台服务启动失败，尝试普通服务: ${e.message}")
                startService(intent)
                LogUtils.d(TAG, "DDS测试服务启动成功 (普通服务)")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动DDS测试服务失败", e)
        }
    }
}
