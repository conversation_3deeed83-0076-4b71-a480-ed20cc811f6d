package com.seres.dds.test.utils

import android.util.Log

/**
 * 日志工具类
 */
object LogUtils {
    
    private const val TAG_PREFIX = "DdsTest-"
    
    fun d(tag: String, message: String) {
        Log.d(TAG_PREFIX + tag, message)
    }
    
    fun i(tag: String, message: String) {
        Log.i(TAG_PREFIX + tag, message)
    }
    
    fun w(tag: String, message: String) {
        Log.w(TAG_PREFIX + tag, message)
    }
    
    fun e(tag: String, message: String) {
        Log.e(TAG_PREFIX + tag, message)
    }
    
    fun e(tag: String, message: String, throwable: Throwable) {
        Log.e(TAG_PREFIX + tag, message, throwable)
    }
}
