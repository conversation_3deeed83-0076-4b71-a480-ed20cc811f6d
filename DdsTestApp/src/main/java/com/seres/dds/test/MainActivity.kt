package com.seres.dds.test

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.seres.dds.test.service.DdsTestService
import com.seres.dds.test.utils.LogUtils
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : AppCompatActivity(), DdsTestService.ServiceCallback {

    private val TAG = "MainActivity"
    
    private lateinit var statusText: TextView
    private lateinit var receivedMessagesText: TextView
    private lateinit var sentMessagesText: TextView
    private lateinit var startServiceButton: Button
    private lateinit var stopServiceButton: Button
    private lateinit var sendTestDataButton: Button
    private lateinit var clearLogButton: Button
    private lateinit var mainScrollView: ScrollView
    private lateinit var receivedScrollView: ScrollView
    private lateinit var sentScrollView: ScrollView
    
    private var ddsTestService: DdsTestService? = null
    private var isServiceBound = false
    private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as DdsTestService.LocalBinder
            ddsTestService = binder.getService()
            ddsTestService?.setServiceCallback(this@MainActivity)
            isServiceBound = true
            updateServiceStatus(ddsTestService?.isConnected() ?: false)
            LogUtils.i(TAG, "服务已连接")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            ddsTestService = null
            isServiceBound = false
            updateServiceStatus(false)
            LogUtils.i(TAG, "服务已断开")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        setupClickListeners()
        
        LogUtils.i(TAG, "MainActivity创建完成")
    }

    override fun onStart() {
        super.onStart()
        // 绑定服务
        val intent = Intent(this, DdsTestService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        // 解绑服务
        if (isServiceBound) {
            ddsTestService?.setServiceCallback(null)
            unbindService(serviceConnection)
            isServiceBound = false
        }
    }

    private fun initViews() {
        statusText = findViewById(R.id.statusText)
        receivedMessagesText = findViewById(R.id.receivedMessagesText)
        sentMessagesText = findViewById(R.id.sentMessagesText)
        startServiceButton = findViewById(R.id.startServiceButton)
        stopServiceButton = findViewById(R.id.stopServiceButton)
        sendTestDataButton = findViewById(R.id.sendTestDataButton)
        clearLogButton = findViewById(R.id.clearLogButton)
        mainScrollView = findViewById(R.id.scrollView)

        // 获取消息区域的ScrollView引用
        receivedScrollView = findViewById(R.id.receivedScrollView)
        sentScrollView = findViewById(R.id.sentScrollView)

        updateServiceStatus(false)
    }

    private fun setupClickListeners() {
        startServiceButton.setOnClickListener {
            startDdsTestService()
        }

        stopServiceButton.setOnClickListener {
            stopDdsTestService()
        }

        sendTestDataButton.setOnClickListener {
            sendTestData()
        }

        clearLogButton.setOnClickListener {
            clearLogs()
        }
    }

    private fun startDdsTestService() {
        try {
            val intent = Intent(this, DdsTestService::class.java)
            ContextCompat.startForegroundService(this, intent)
            LogUtils.i(TAG, "启动DDS测试服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动服务失败: ${e.message}")
        }
    }

    private fun stopDdsTestService() {
        try {
            val intent = Intent(this, DdsTestService::class.java)
            stopService(intent)
            LogUtils.i(TAG, "停止DDS测试服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止服务失败: ${e.message}")
        }
    }

    private fun sendTestData() {
        ddsTestService?.sendInventoryInfoResponse()
        LogUtils.i(TAG, "手动发送测试数据")
    }

    private fun clearLogs() {
        receivedMessagesText.text = ""
        sentMessagesText.text = ""
        LogUtils.i(TAG, "清空日志")
    }

    private fun updateServiceStatus(isRunning: Boolean) {
        runOnUiThread {
            val status = if (isRunning) "运行中" else "已停止"
            statusText.text = getString(R.string.service_status, status)
            
            startServiceButton.isEnabled = !isRunning
            stopServiceButton.isEnabled = isRunning
            sendTestDataButton.isEnabled = isRunning
        }
    }

    private fun appendMessage(textView: TextView, message: String) {
        runOnUiThread {
            val timestamp = dateFormat.format(Date())
            val formattedMessage = "[$timestamp] $message\n"

            // 添加消息到TextView
            textView.append(formattedMessage)

            // 根据TextView确定对应的ScrollView并滚动到底部
            val targetScrollView = when (textView.id) {
                R.id.receivedMessagesText -> receivedScrollView
                R.id.sentMessagesText -> sentScrollView
                else -> null
            }

            // 强制滚动到底部
            targetScrollView?.let { scrollView ->
                scrollView.post {
                    // 使用多种方法确保滚动到底部
                    scrollView.fullScroll(ScrollView.FOCUS_DOWN)

                    // 延迟再次滚动，确保内容已经渲染
                    scrollView.postDelayed({
                        scrollView.scrollTo(0, textView.height)
                    }, 100)
                }
            }

            // 同时滚动主ScrollView，确保消息区域可见
            mainScrollView.post {
                mainScrollView.fullScroll(ScrollView.FOCUS_DOWN)
            }
        }
    }

    // ServiceCallback implementations
    override fun onUpgradeTaskReceived(taskInfo: String) {
        LogUtils.i(TAG, "收到升级任务: $taskInfo")
        appendMessage(receivedMessagesText, "升级任务: ${taskInfo.take(100)}...")
    }

    override fun onInventoryInfoSent(inventoryInfo: String) {
        LogUtils.i(TAG, "发送资产信息: $inventoryInfo")
        appendMessage(sentMessagesText, "资产信息: ${inventoryInfo.take(100)}...")
    }

    override fun onServiceStatusChanged(isRunning: Boolean) {
        LogUtils.i(TAG, "服务状态变化: $isRunning")
        updateServiceStatus(isRunning)
    }
}
