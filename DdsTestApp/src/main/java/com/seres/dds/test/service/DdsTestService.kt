package com.seres.dds.test.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.system.Os.getenv
import android.system.Os.setenv
import androidx.core.app.NotificationCompat
import com.google.gson.Gson
import com.seres.dds.sdk.*
import com.seres.dds.test.model.InventoryInfo
import com.seres.dds.test.model.InventoryInfoStatus
import com.seres.dds.test.utils.LogUtils
import seres.UDiskSrv_OTAM_Control_Topic.UDiskSrv_OTAM_Control
import seres.OTAM_UDiskSrv_Status_Topic.OTAM_UDiskSrv_Status
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * DDS测试服务
 * 订阅升级任务信息，发布资产信息状态
 */
class DdsTestService : Service() {

    private val TAG = "DdsTestService"
    private val gson = Gson()
    private val executor: ScheduledExecutorService = Executors.newScheduledThreadPool(2)

    // DDS实体
    private var domainParticipant: DomainParticipant? = null

    // 订阅者相关 - 订阅升级任务信息
    private var subscriber: Subscriber? = null
    private var upgradeTaskTopic: Topic? = null
    private var upgradeTaskReader: DataReader? = null

    // 发布者相关 - 发布资产信息状态
    private var publisher: Publisher? = null
    private var inventoryTopic: Topic? = null
    private var inventoryWriter: DataWriter? = null

    // 服务状态
    private var isInitialized = false
    private var serviceCallback: ServiceCallback? = null

    // Binder for activity communication
    private val binder = LocalBinder()

    companion object {
        const val NOTIFICATION_ID = 3001
        const val CHANNEL_ID = "dds_test_channel"
    }

    /**
     * 服务回调接口
     */
    interface ServiceCallback {
        fun onUpgradeTaskReceived(taskInfo: String)
        fun onInventoryInfoSent(inventoryInfo: String)
        fun onServiceStatusChanged(isRunning: Boolean)
    }

    inner class LocalBinder : Binder() {
        fun getService(): DdsTestService = this@DdsTestService
    }

    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "DdsTestService onCreate")

        // 创建前台服务通知
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())

        // 初始化DDS
        initializeDds()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "DdsTestService onStartCommand")

        // 确保前台服务正常运行
        try {
            startForeground(NOTIFICATION_ID, createNotification())
        } catch (e: Exception) {
            LogUtils.e(TAG, "更新前台服务通知失败: ${e.message}")
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "DdsTestService onDestroy")
        cleanup()
    }

    /**
     * 设置服务回调
     */
    fun setServiceCallback(callback: ServiceCallback?) {
        this.serviceCallback = callback
    }

    /**
     * 初始化DDS
     */
    private fun initializeDds() {
        try {
            LogUtils.i(TAG, "初始化DDS服务")
            setenv("CYCLONEDDS_URI",
                "<CycloneDDS><Domain id=\"any\"><General><NetworkInterfaceAddress>lo</NetworkInterfaceAddress></General></Domain></CycloneDDS>",
                true)
            val envVal = getenv("CYCLONEDDS_URI")
            LogUtils.e(TAG, "ENV: CYCLONEDDS_URI=$envVal")
            // 创建DomainParticipant
            domainParticipant = DomainParticipant(42)
            LogUtils.i(TAG, "DomainParticipant创建成功")

            // 初始化订阅者
            initializeSubscriber()

            // 初始化发布者
            initializePublisher()

            // 启动数据读取任务
            startUpgradeTaskReading()

            isInitialized = true
            serviceCallback?.onServiceStatusChanged(true)
            LogUtils.i(TAG, "DDS服务初始化完成")

        } catch (e: Exception) {
            LogUtils.e(TAG, "DDS服务初始化失败: ${e.message}", e)
            serviceCallback?.onServiceStatusChanged(false)
        }
    }

    /**
     * 初始化订阅者 - 订阅升级任务信息
     */
    private fun initializeSubscriber() {
        // 创建Subscriber
        subscriber = Subscriber(domainParticipant!!)
        LogUtils.i(TAG, "Subscriber创建成功")

        // 创建升级任务Topic和DataReader
        upgradeTaskTopic = Topic(domainParticipant!!, "UDiskSrv_OTAM_Control", UDiskSrv_OTAM_Control())
        upgradeTaskReader = DataReader(subscriber!!, upgradeTaskTopic!!)
        LogUtils.i(TAG, "升级任务Topic和DataReader创建成功")
    }

    /**
     * 初始化发布者 - 发布资产信息状态
     */
    private fun initializePublisher() {
        // 创建Publisher
        publisher = Publisher(domainParticipant!!)
        LogUtils.i(TAG, "Publisher创建成功")

        // 创建资产信息Topic和DataWriter
        inventoryTopic = Topic(domainParticipant!!, "OTAM_UDiskSrv_Status", OTAM_UDiskSrv_Status())
        inventoryWriter = DataWriter(publisher!!, inventoryTopic!!)
        LogUtils.i(TAG, "资产信息Topic和DataWriter创建成功")
    }

    /**
     * 启动升级任务数据读取
     */
    private fun startUpgradeTaskReading() {
        executor.scheduleWithFixedDelay({
            try {
                readUpgradeTaskData()
            } catch (e: Exception) {
                LogUtils.e(TAG, "读取升级任务数据时出错: ${e.message}", e)
            }
        }, 1, 1, TimeUnit.SECONDS) // 每秒检查一次
    }

    /**
     * 读取升级任务数据
     */
    private fun readUpgradeTaskData() {
        upgradeTaskReader?.let { reader ->
            val samples = reader.take(10) // 一次最多读取10个样本

            if (samples.sample_count > 0) {
                LogUtils.d(TAG, "收到 ${samples.sample_count} 个升级任务样本")

                samples.sample_list?.forEach { sample ->
                    sample.info?.let { info ->
                        if (info.valid_data) {
                            val upgradeTaskControl = sample.type as UDiskSrv_OTAM_Control.descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO
                            val upgradeTaskInfo = upgradeTaskControl.upgrade_task_info

                            LogUtils.i(TAG, "收到升级任务信息: $upgradeTaskInfo")

                            // 通知UI
                            upgradeTaskInfo?.let { taskInfo ->
                                serviceCallback?.onUpgradeTaskReceived(taskInfo)

                                // 自动发送资产信息响应
                                sendInventoryInfoResponse()
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 发送资产信息响应
     */
    fun sendInventoryInfoResponse() {
        try {
            LogUtils.i(TAG, "发送资产信息响应")

            // 创建测试资产信息
            val inventoryList = listOf(
                InventoryInfo(
                    ecuName = "HPCM",
                    softwareVersion = "1.0.0",
                    partNumber = "HPCM-001",
                    supplierCode = "SERES",
                    serialNumber = "SN001",
                    hardwareVersion = "HW1.0",
                    bootloaderVersion = "BL1.0",
                    backupVersion = "BK1.0"
                ),
                InventoryInfo(
                    ecuName = "TBOX",
                    softwareVersion = "2.1.0",
                    partNumber = "TBOX-002",
                    supplierCode = "SERES",
                    serialNumber = "SN002",
                    hardwareVersion = "HW2.0",
                    bootloaderVersion = "BL2.0",
                    backupVersion = "BK2.0"
                )
            )

            val inventoryStatus = InventoryInfoStatus(
                inventoryList = inventoryList,
                timestamp = System.currentTimeMillis(),
                status = "SUCCESS"
            )

            // 转换为JSON
            val inventoryJson = gson.toJson(inventoryStatus)

            // 创建OTAM_UDiskSrv_Status实例
            val otamStatus = OTAM_UDiskSrv_Status()
            otamStatus.inventory_info = inventoryJson

            // 发布数据
            val result = inventoryWriter?.write(otamStatus) ?: -1

            if (result == 0) {
                LogUtils.i(TAG, "资产信息发布成功")
                serviceCallback?.onInventoryInfoSent(inventoryJson)
            } else {
                LogUtils.e(TAG, "资产信息发布失败，错误码: $result")
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "发送资产信息时出错: ${e.message}", e)
        }
    }

    /**
     * 检查DDS连接状态
     */
    fun isConnected(): Boolean {
        return isInitialized && domainParticipant != null &&
               publisher != null && inventoryWriter != null &&
               subscriber != null && upgradeTaskReader != null
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "DDS测试服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "DDS测试服务运行状态"
            setShowBadge(false)
        }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("DDS测试服务")
            .setContentText("正在监听DDS消息")
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            LogUtils.i(TAG, "清理DDS资源")

            // 停止定时任务
            executor.shutdownNow()

            // 清理DDS实体
            upgradeTaskReader = null
            inventoryWriter = null
            upgradeTaskTopic = null
            inventoryTopic = null
            publisher = null
            subscriber = null
            domainParticipant = null

            isInitialized = false
            serviceCallback?.onServiceStatusChanged(false)

            LogUtils.i(TAG, "DDS资源清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理DDS资源时出错: ${e.message}", e)
        }
    }
}
