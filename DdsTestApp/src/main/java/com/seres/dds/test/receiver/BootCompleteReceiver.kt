package com.seres.dds.test.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.seres.dds.test.service.DdsTestService
import com.seres.dds.test.utils.LogUtils

/**
 * 开机启动接收器
 * 系统启动后自动启动DDS测试服务
 */
class BootCompleteReceiver : BroadcastReceiver() {
    
    private val TAG = "BootCompleteReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_LOCKED_BOOT_COMPLETED -> {
                LogUtils.i(TAG, "系统启动完成，启动DDS测试服务")
                
                try {
                    // 启动DDS测试服务
                    val serviceIntent = Intent(context, DdsTestService::class.java)
                    context.startForegroundService(serviceIntent)
                    
                    LogUtils.i(TAG, "DDS测试服务启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动DDS测试服务失败: ${e.message}")
                }
            }
        }
    }
}
