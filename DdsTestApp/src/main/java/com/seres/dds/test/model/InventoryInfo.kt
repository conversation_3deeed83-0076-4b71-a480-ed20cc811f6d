package com.seres.dds.test.model

/**
 * 资产信息数据模型
 */
data class InventoryInfo(
    val ecuName: String = "",
    val softwareVersion: String = "",
    val partNumber: String = "",
    val supplierCode: String = "",
    val serialNumber: String = "",
    val hardwareVersion: String = "",
    val bootloaderVersion: String = "",
    val backupVersion: String = ""
)

/**
 * 资产信息状态
 */
data class InventoryInfoStatus(
    val inventoryList: List<InventoryInfo> = emptyList(),
    val timestamp: Long = System.currentTimeMillis(),
    val status: String = "SUCCESS"
)
