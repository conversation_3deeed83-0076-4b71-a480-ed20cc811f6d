package seres.UDiskSrv_OTAM_Control_Topic

enum class UDiskSrvOtamCtrlType(var basevalue : Int){
    UPGRADE_TASK_INFO(0),
    UPGRADE_TASK_IS_MATCH(1);


    companion object {
        private val valueMap = UDiskSrvOtamCtrlType.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): UDiskSrvOtamCtrlType{
            return  valueMap[basevalue]?:UPGRADE_TASK_INFO
        }
    }
}
