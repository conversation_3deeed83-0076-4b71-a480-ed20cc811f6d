package seres.UDiskSrv_OTAM_Control_Topic


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class UDiskSrv_OTAM_Control : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "seres::UDiskSrv_OTAM_Control_Topic::UDiskSrv_OTAM_Control"
        dmutableMap.put(seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO.basevalue, descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO::class)
        dmutableMap.put(seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH.basevalue, descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH::class)
    }

    var _d: seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType
        get() = seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.values().first{ it.basevalue == __d }
        set(value) {
            __d = value.basevalue
        }

    class descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_info: seres.UDiskSrv_OTAM_Control_Topic.UpgradeTaskInfo = ""

        init{
            _d = seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_info", false))

            __u = this

            initproperty()
        }

        var upgrade_task_info: seres.UDiskSrv_OTAM_Control_Topic.UpgradeTaskInfo
            get() = _upgrade_task_info
            set(value) {
                _upgrade_task_info = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_info=$upgrade_task_info)"
        }

    }

    class descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH() : UDiskSrv_OTAM_Control(){
        private var _upgrade_task_is_match: seres.UDiskSrv_OTAM_Control_Topic.UpgradeTaskIsMatch = false

        init{
            _d = seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_upgrade_task_is_match", false))

            __u = this

            initproperty()
        }

        var upgrade_task_is_match: seres.UDiskSrv_OTAM_Control_Topic.UpgradeTaskIsMatch
            get() = _upgrade_task_is_match
            set(value) {
                _upgrade_task_is_match = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, upgrade_task_is_match=$upgrade_task_is_match)"
        }

    }

    var _u_descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO: descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO
        get(){
            __u?.let{
                if ((__u as descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO)._d == seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_INFO){
                    return __u!! as descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }

    var _u_descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH: descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH
        get(){
            __u?.let{
                if ((__u as descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH)._d == seres.UDiskSrv_OTAM_Control_Topic.UDiskSrvOtamCtrlType.UPGRADE_TASK_IS_MATCH){
                    return __u!! as descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH
                }else{
                    throw IllegalArgumentException("Error: union _d not match")
                }
            }?: throw IllegalArgumentException("Error: _u is not set")
        }

        set(@Suppress("UNUSED_PARAMETER") value){
            throw IllegalArgumentException("Error: can not set value to _u")
        }


}

