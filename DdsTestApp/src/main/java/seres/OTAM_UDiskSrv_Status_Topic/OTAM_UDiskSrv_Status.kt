package seres.OTAM_UDiskSrv_Status_Topic


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class OTAM_UDiskSrv_Status : TypeStruct() {
    private var _inventory_info : seres.OTAM_UDiskSrv_Status_Topic.InventoryInfoStatus = ""

    init{
        keyless = true
        version_support = 1
        typename = "seres::OTAM_UDiskSrv_Status_Topic::OTAM_UDiskSrv_Status"
        orderedMembers = arrayListOf(
            Member("_inventory_info", false),
        )

        initproperty()
    }

    var inventory_info: seres.OTAM_UDiskSrv_Status_Topic.InventoryInfoStatus
        get() = _inventory_info
        set(value){
            _inventory_info = value
        }

    fun copy(value: OTAM_UDiskSrv_Status = this): OTAM_UDiskSrv_Status{
        this._inventory_info =  value._inventory_info
        return this
    }

    override fun toString(): String{
        return "$typename(inventory_info=$inventory_info)"
    }
}

