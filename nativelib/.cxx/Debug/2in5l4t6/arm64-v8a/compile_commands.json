[{"directory": "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/arm64-v8a", "command": "D:\\Android\\Sdk\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=aarch64-none-linux-android31 --sysroot=D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dkscom_EXPORTS -IC:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC -o CMakeFiles\\kscom.dir\\kscom.c.o -c C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\kscom.c", "file": "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\kscom.c"}, {"directory": "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/arm64-v8a", "command": "D:\\Android\\Sdk\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android31 --sysroot=D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dkscom_EXPORTS -IC:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles\\kscom.dir\\kscom_bridge.cpp.o -c C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\kscom_bridge.cpp", "file": "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\kscom_bridge.cpp"}]