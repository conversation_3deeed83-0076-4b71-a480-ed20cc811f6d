                        -HC:\Users\<USER>\Desktop\ota-android\nativelib\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=31
-DANDROID_PLATFORM=android-31
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\Android\Sdk\ndk\27.2.12479018
-DCMAKE_ANDROID_NDK=D:\Android\Sdk\ndk\27.2.12479018
-DCMAKE_TOOLCHAIN_FILE=D:\Android\Sdk\ndk\27.2.12479018\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\ota-android\nativelib\build\intermediates\cxx\Debug\2in5l4t6\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Desktop\ota-android\nativelib\build\intermediates\cxx\Debug\2in5l4t6\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\arm64-v8a
-GNinja
                        Build command args: []
                        Version: 2