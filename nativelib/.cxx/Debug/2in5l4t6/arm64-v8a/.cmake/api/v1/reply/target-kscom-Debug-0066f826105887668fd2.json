{"artifacts": [{"path": "C:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/Debug/2in5l4t6/obj/arm64-v8a/libkscom.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 44, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}, {"command": 3, "file": 0, "line": 14, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC"}], "defines": [{"define": "kscom_EXPORTS"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include"}], "language": "C", "sourceIndexes": [0], "sysroot": {"path": "D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, {"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}], "defines": [{"define": "kscom_EXPORTS"}], "includes": [{"backtrace": 4, "path": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include"}], "language": "CXX", "sourceIndexes": [1], "sysroot": {"path": "D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "kscom::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a"}], "prefix": {"path": "C:/Program Files (x86)/nativelib"}}, "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\libs\\arm64-v8a\\libddsc.so", "role": "libraries"}, {"backtrace": 3, "fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "kscom", "nameOnDisk": "libkscom.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "kscom.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "kscom_bridge.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}