{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-ef42d218f111cb6c62f8.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "nativelib", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "kscom::@6890427a1f51a3e7e1df", "jsonFile": "target-kscom-Debug-0066f826105887668fd2.json", "name": "kscom", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/arm64-v8a", "source": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp"}, "version": {"major": 2, "minor": 3}}