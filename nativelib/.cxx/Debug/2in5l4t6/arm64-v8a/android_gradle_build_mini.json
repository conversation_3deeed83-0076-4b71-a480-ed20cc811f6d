{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\.cxx\\Debug\\2in5l4t6\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\.cxx\\Debug\\2in5l4t6\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"kscom::@6890427a1f51a3e7e1df": {"artifactName": "kscom", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\build\\intermediates\\cxx\\Debug\\2in5l4t6\\obj\\arm64-v8a\\libkscom.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\libs\\arm64-v8a\\libddsc.so"]}}}