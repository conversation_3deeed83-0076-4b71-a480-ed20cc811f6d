# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: nativelib
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/x86_64/
# =============================================================================
# Object build statements for SHARED_LIBRARY target kscom


#############################################
# Order-only phony target for kscom

build cmake_object_order_depends_target_kscom: phony || CMakeFiles/kscom.dir

build CMakeFiles/kscom.dir/kscom.c.o: C_COMPILER__kscom_Debug C$:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/kscom.c || cmake_object_order_depends_target_kscom
  DEFINES = -Dkscom_EXPORTS
  DEP_FILE = CMakeFiles\kscom.dir\kscom.c.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\kscom.dir
  OBJECT_FILE_DIR = CMakeFiles\kscom.dir

build CMakeFiles/kscom.dir/kscom_bridge.cpp.o: CXX_COMPILER__kscom_Debug C$:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/kscom_bridge.cpp || cmake_object_order_depends_target_kscom
  DEFINES = -Dkscom_EXPORTS
  DEP_FILE = CMakeFiles\kscom.dir\kscom_bridge.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC
  INCLUDES = -IC:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/include
  OBJECT_DIR = CMakeFiles\kscom.dir
  OBJECT_FILE_DIR = CMakeFiles\kscom.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target kscom


#############################################
# Link the shared library C:\Users\<USER>\Desktop\ota-android\nativelib\build\intermediates\cxx\Debug\2in5l4t6\obj\x86_64\libkscom.so

build C$:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/Debug/2in5l4t6/obj/x86_64/libkscom.so: CXX_SHARED_LIBRARY_LINKER__kscom_Debug CMakeFiles/kscom.dir/kscom.c.o CMakeFiles/kscom.dir/kscom_bridge.cpp.o | C$:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/libs/x86_64/libddsc.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/libs/x86_64/libddsc.so  -llog  -latomic -lm
  OBJECT_DIR = CMakeFiles\kscom.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libkscom.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\Users\<USER>\Desktop\ota-android\nativelib\build\intermediates\cxx\Debug\2in5l4t6\obj\x86_64\libkscom.so
  TARGET_PDB = kscom.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64 && D:\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64 && D:\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\ota-android\nativelib\src\main\cpp -BC:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64 && D:\Android\Sdk\cmake\3.22.1\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64 && D:\Android\Sdk\cmake\3.22.1\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\Desktop\ota-android\nativelib\.cxx\Debug\2in5l4t6\x86_64 && D:\Android\Sdk\cmake\3.22.1\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build kscom: phony C$:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/Debug/2in5l4t6/obj/x86_64/libkscom.so

build libkscom.so: phony C$:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/Debug/2in5l4t6/obj/x86_64/libkscom.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/x86_64

build all: phony C$:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/Debug/2in5l4t6/obj/x86_64/libkscom.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/abis.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/android-legacy.toolchain.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/flags.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android-Clang.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android-Initialize.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/abis.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/android-legacy.toolchain.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/flags.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android-Clang.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android-Initialize.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/hooks/pre/Android.cmake D$:/Android/Sdk/ndk/27.2.12479018/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
