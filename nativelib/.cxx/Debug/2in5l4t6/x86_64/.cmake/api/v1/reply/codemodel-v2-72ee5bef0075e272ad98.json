{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-cd5bb9ed0791209d6be9.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "nativelib", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "kscom::@6890427a1f51a3e7e1df", "jsonFile": "target-kscom-Debug-401e5c3c21b14e334b73.json", "name": "kscom", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/Debug/2in5l4t6/x86_64", "source": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp"}, "version": {"major": 2, "minor": 3}}