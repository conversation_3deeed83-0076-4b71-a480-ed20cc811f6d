# Install script for directory: C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/nativelib")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "RelWithDebInfo")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "0")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-objdump.exe")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/ota-android/nativelib/build/intermediates/cxx/RelWithDebInfo/4u6e3rk1/obj/arm64-v8a/libkscom.so")
  if(EXISTS "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "D:/Android/Sdk/ndk/27.2.12479018/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe" "$ENV{DESTDIR}/C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp/../jniLibs/arm64-v8a/libkscom.so")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/RelWithDebInfo/4u6e3rk1/arm64-v8a/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
