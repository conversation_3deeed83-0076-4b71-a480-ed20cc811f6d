{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-894da9eef689e8c5d15f.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "nativelib", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "kscom::@6890427a1f51a3e7e1df", "jsonFile": "target-kscom-RelWithDebInfo-6d14d8dcd67a1f305ec7.json", "name": "kscom", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/ota-android/nativelib/.cxx/RelWithDebInfo/4u6e3rk1/x86_64", "source": "C:/Users/<USER>/Desktop/ota-android/nativelib/src/main/cpp"}, "version": {"major": 2, "minor": 3}}