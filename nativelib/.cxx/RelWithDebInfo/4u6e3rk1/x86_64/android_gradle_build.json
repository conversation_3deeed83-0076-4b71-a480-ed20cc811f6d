{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\.cxx\\RelWithDebInfo\\4u6e3rk1\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\.cxx\\RelWithDebInfo\\4u6e3rk1\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"kscom::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "kscom", "output": "C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\build\\intermediates\\cxx\\RelWithDebInfo\\4u6e3rk1\\obj\\x86_64\\libkscom.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\ota-android\\nativelib\\src\\main\\cpp\\libs\\x86_64\\libddsc.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Android\\Sdk\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Android\\Sdk\\ndk\\27.2.12479018\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": ["c"], "cppFileExtensions": ["cpp"]}