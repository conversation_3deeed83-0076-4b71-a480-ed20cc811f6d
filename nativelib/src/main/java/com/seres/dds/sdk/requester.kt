package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.StatusMask
import com.seres.dds.sdk.idl.TypeBase

open class RequesterParams {
    private var participant: DomainParticipant
    private var max_wait: Long
    private var service_name: String
    private var wr_qos: Qos?
    private var rd_qos: Qos?

    constructor(par: DomainParticipant, wrQos: Qos?, rdQos: Qos?) {
        participant = par
        max_wait = 3000000000L
        service_name = ""
        wr_qos = wrQos
        rd_qos = rdQos
    }

    fun get_participant(): DomainParticipant {
        return participant
    }

    fun set_participant(par: DomainParticipant) {
        participant = par
    }

    fun getMaxWait(): Long {
        return max_wait
    }

    fun setMaxWait(maxWait: Long) {
        this.max_wait = maxWait
    }

    fun get_service_name(): String {
        return service_name
    }

    fun set_service_name(name: String) {
        service_name = name
    }

     fun get_wr_qos():Qos?{
         return wr_qos
     }

     fun set_wr_qos(qos:Qos){
         wr_qos = qos
     }

     fun get_rd_qos():Qos?{
         return rd_qos
     }

     fun set_rd_qos(qos:Qos){
         rd_qos = qos
     }
}

class Requester {

    private var participant: DomainParticipant
    private var writer: DataWriter
    private var reader: DataReader
    private var service_name: String
    private var waitset: WaitSet
    private var readcond : ReadCondition

    constructor(params: RequesterParams, reqtype: TypeBase, reptype: TypeBase) {
        participant = params.get_participant()
        service_name = params.get_service_name()

        var requesttp = Topic(participant, service_name + "Request", reqtype)

        writer = DataWriter(participant, requesttp, params.get_wr_qos())
        println(TAG + "Requester requesttp ${requesttp.ref}")
        println(TAG + "Requester writer ${writer.ref}")
        var replytp = Topic(participant, service_name + "Reply", reptype)

        reader = DataReader(participant, replytp, params.get_rd_qos())
        println(TAG + "Requester requesttp ${replytp.ref}")
        println(TAG + "Requester reader ${reader.ref}")
        //create readcondition
        //create waitset

        readcond = ReadCondition(reader, StatusMask.any_state())

        waitset = WaitSet(params.get_participant())
        waitset.attach(readcond)
    }

    fun send_request(request: TypeBase) {
        writer.write(request)
    }

    fun receive_reply(): TypeBase? {
        val samples = reader.take()
        var result: TypeBase? = null
        samples.sample_list?.forEach { sample ->
            if (sample.info?.valid_data == true) {
                println(TAG +"sample.type.typename:${sample.type.gettypename()}")
                result = sample.type

            }
        }
        println(TAG +"result:${result?.gettypename()}")
        return result
    }

    fun wait_for_replies(maxwait: Long): Boolean {

        println(TAG +"wait for replies: ${waitset.ref}, maxwait : ${maxwait}")
        if (maxwait == 0L) {
            println(TAG + "maxwait must bigger than 0")
        }
        return waitset.wait(TAG, maxwait) > 0
    }

    fun get_reader(): DataReader {
        return reader
    }

    fun get_writer(): DataWriter {
        return writer
    }

    companion object {
        private const val TAG = "Requester"
    }
}
