package com.seres.dds.sdk

import com.seres.dds.sdk.KScomNativeLib.kScomCreateQos
import com.seres.dds.sdk.KScomNativeLib.kScomGetBinaryProperty
import com.seres.dds.sdk.KScomNativeLib.kScomGetDataRepresentation
import com.seres.dds.sdk.KScomNativeLib.kScomGetDeadline
import com.seres.dds.sdk.KScomNativeLib.kScomGetDestinationOrder
import com.seres.dds.sdk.KScomNativeLib.kScomGetDurability
import com.seres.dds.sdk.KScomNativeLib.kScomGetDurabilityService
import com.seres.dds.sdk.KScomNativeLib.kScomGetEntityName
import com.seres.dds.sdk.KScomNativeLib.kScomGetGroupdata
import com.seres.dds.sdk.KScomNativeLib.kScomGetIgnoreLocal
import com.seres.dds.sdk.KScomNativeLib.kScomGetLatencyBudget
import com.seres.dds.sdk.KScomNativeLib.kScomGetLifespan
import com.seres.dds.sdk.KScomNativeLib.kScomGetLiveliness
import com.seres.dds.sdk.KScomNativeLib.kScomGetOwnership
import com.seres.dds.sdk.KScomNativeLib.kScomGetOwnershipStrength
import com.seres.dds.sdk.KScomNativeLib.kScomGetPartition
import com.seres.dds.sdk.KScomNativeLib.kScomGetPresentation
import com.seres.dds.sdk.KScomNativeLib.kScomGetProperty
import com.seres.dds.sdk.KScomNativeLib.kScomGetReaderDataLifecycle
import com.seres.dds.sdk.KScomNativeLib.kScomGetReliability
import com.seres.dds.sdk.KScomNativeLib.kScomGetResourceLimits
import com.seres.dds.sdk.KScomNativeLib.kScomGetTimeBasedFilter
import com.seres.dds.sdk.KScomNativeLib.kScomGetTopicdata
import com.seres.dds.sdk.KScomNativeLib.kScomGetTransportPriority
import com.seres.dds.sdk.KScomNativeLib.kScomGetTypeConsistency
import com.seres.dds.sdk.KScomNativeLib.kScomGetUserdata
import com.seres.dds.sdk.KScomNativeLib.kScomGetWriteDataLifecycle
import com.seres.dds.sdk.KScomNativeLib.kScomSetBinaryproperty
import com.seres.dds.sdk.KScomNativeLib.kScomSetDataRepresentation
import com.seres.dds.sdk.KScomNativeLib.kScomSetDeadline
import com.seres.dds.sdk.KScomNativeLib.kScomSetDestinationOrder
import com.seres.dds.sdk.KScomNativeLib.kScomSetDurability
import com.seres.dds.sdk.KScomNativeLib.kScomSetDurabilityservice
import com.seres.dds.sdk.KScomNativeLib.kScomSetEntityName
import com.seres.dds.sdk.KScomNativeLib.kScomSetGroupdata
import com.seres.dds.sdk.KScomNativeLib.kScomSetIgnorelocal
import com.seres.dds.sdk.KScomNativeLib.kScomSetLatencyBudget
import com.seres.dds.sdk.KScomNativeLib.kScomSetLifespan
import com.seres.dds.sdk.KScomNativeLib.kScomSetLiveliness
import com.seres.dds.sdk.KScomNativeLib.kScomSetOwnership
import com.seres.dds.sdk.KScomNativeLib.kScomSetOwnershipStrength
import com.seres.dds.sdk.KScomNativeLib.kScomSetPresentation
import com.seres.dds.sdk.KScomNativeLib.kScomSetProperty
import com.seres.dds.sdk.KScomNativeLib.kScomSetReaderDataLifecycle
import com.seres.dds.sdk.KScomNativeLib.kScomSetReliability
import com.seres.dds.sdk.KScomNativeLib.kScomSetResourceLimits
import com.seres.dds.sdk.KScomNativeLib.kScomSetTimeBasedFilter
import com.seres.dds.sdk.KScomNativeLib.kScomSetTopicdata
import com.seres.dds.sdk.KScomNativeLib.kScomSetTransportPriority
import com.seres.dds.sdk.KScomNativeLib.kScomSetTypeconsistency
import com.seres.dds.sdk.KScomNativeLib.kScomSetUserdata
import com.seres.dds.sdk.KScomNativeLib.kScomSetWriterDataLifecycle
import com.seres.dds.sdk.Policy.BasePolicy


class Policy {

    open class BasePolicy(val scope: String) {
        override fun toString(): String = "Policy.$scope"
    }

    open class Reliability : BasePolicy("Reliability") {
        class BestEffort : Reliability() {
            override fun toString(): String = "Policy.$scope.BestEffort"
        }

        data class Reliable(val maxBlockingTime: Long) : Reliability() {
            override fun toString(): String = "Policy.$scope.Reliable"
        }
    }

    open class Durability : BasePolicy("Durability") {
        class Volatile : Durability() {
            override fun toString(): String = "Policy.$scope.Volatile"
        }

        class TransientLocal : Durability() {
            override fun toString(): String = "Policy.$scope.TransientLocal"
        }

        class Transient : Durability() {
            override fun toString(): String = "Policy.$scope.Transient"
        }

        class Persistent : Durability() {
            override fun toString(): String = "Policy.$scope.Persistent"
        }
    }

    open class History : BasePolicy("History") {
        class KeepAll : History() {
            override fun toString(): String = "Policy.$scope.KeepAll"
        }

        data class KeepLast(val depth: Int) : History() {
            override fun toString(): String = "Policy.$scope.KeepLast"
        }
    }

    data class ResourceLimits(
        val max_samples: Int = -1,
        val max_instances: Int = -1,
        val max_samples_per_instance: Int = -1
    ) : BasePolicy("ResourceLimits")

    open class PresentationAccessScope : BasePolicy("PresentationAccessScope") {

        data class Instance(
            val coherent_access: Boolean,
            val ordered_access: Boolean
        ) : PresentationAccessScope() {
            override fun toString(): String = "Policy.$scope.Instance"
        }

        data class Topic(
            val coherent_access: Boolean,
            val ordered_access: Boolean
        ) : PresentationAccessScope() {
            override fun toString(): String = "Policy.$scope.Topic"
        }

        data class Group(
            val coherent_access: Boolean,
            val ordered_access: Boolean
        ) : PresentationAccessScope() {
            override fun toString(): String = "Policy.$scope.Group"
        }
    }

    data class Lifespan(
        val lifespan: Long
    ) : BasePolicy("Lifespan")

    data class Deadline(
        val deadline: Long
    ) : BasePolicy("Deadline")

    data class LatencyBudget(
        val budget: Long
    ) : BasePolicy("LatencyBudget")

    open class Ownership : BasePolicy("Ownership") {
        class Shared : Ownership() {
            override fun toString(): String = "Policy.$scope.Shared"
        }

        class Exclusive : Ownership() {
            override fun toString(): String = "Policy.$scope.Exclusive"
        }
    }

    data class OwnershipStrength(
        val strength: Int
    ) : BasePolicy("OwnershipStrength")

    open class Liveliness : BasePolicy("Liveliness") {

        data class Automatic(
            val lease_duration: Long
        ) : Liveliness() {
            override fun toString(): String = "Policy.$scope.Automatic"
        }

        data class ManualByParticipant(
            val lease_duration: Long
        ) : Liveliness() {
            override fun toString(): String = "Policy.$scope.ManualByParticipant"
        }

        data class ManualByTopic(
            val lease_duration: Long
        ) : Liveliness() {
            override fun toString(): String = "Policy.$scope.ManualByTopic"
        }

    }

    data class TimeBasedFilter(val filter_time: Long) : BasePolicy("TimeBasedFilter")

    data class Partition(val partitions: ArrayList<String>) : BasePolicy("Partition")

    data class TransportPriority(val priority: Int) : BasePolicy("TransportPriority")

    open class DestinationOrder : BasePolicy("DestinationOrder") {
        class ByReceptionTimestamp : DestinationOrder() {
            override fun toString(): String = "Policy.DestinationOrder.ByReceptionTimestamp"
        }

        class BySourceTimestamp : DestinationOrder() {
            override fun toString(): String = "Policy.DestinationOrder.BySourceTimestamp"
        }
    }

    data class WriteDataLifecycle(val autodispose: Boolean) : BasePolicy("WriteDataLifecycle")

    data class ReaderDataLifecycle(
        val autopurge_nowriter_samples_delay: Long,
        val autopurge_disposed_samples_delay: Long
    ) : BasePolicy("ReaderDataLifecycle")


    data class DurabilityService(
        val cleanup_delay: Long,
        val history: Policy.History,
        val max_samples: Int,
        val max_instances: Int,
        val max_samples_per_instance: Int
    ) : BasePolicy("DurabilityService") {

    }

    open class IgnoreLocal : BasePolicy("IgnoreLocal") {
        class Nothing : IgnoreLocal() {
            override fun toString(): String = "Policy.$scope.Nothing"
        }

        class Participant : IgnoreLocal() {
            override fun toString(): String = "Policy.$scope.Participant"
        }

        class Process : IgnoreLocal() {
            override fun toString(): String = "Policy.$scope.Process"
        }
    }

    data class Userdata(
        val data: ByteArray
    ) : BasePolicy("Userdata")

    data class Topicdata(
        val data: ByteArray
    ) : BasePolicy("Topicdata")

    data class Groupdata(
        val data: ByteArray
    ) : BasePolicy("Topicdata")

    data class Property(
        val key: String,
        val value: String
    ) : BasePolicy("Property")

    data class BinaryProperty(val key: String, val value: ByteArray) : BasePolicy("BinaryProperty")

    open class TypeConsistency : BasePolicy("TypeConsistency") {
        data class DisallowTypeCoercion(val force_type_validation: Boolean) : TypeConsistency() {
            override fun toString(): String = "Policy.$scope.DisallowTypeCoercion"
        }

        data class AllowTypeCoercion(
            val ignore_sequence_bounds: Boolean = true,
            val ignore_string_bounds: Boolean = true,
            val ignore_member_names: Boolean = true,
            val prevent_type_widening: Boolean = false,
            val force_type_validation: Boolean = false
        ) : TypeConsistency() {
            override fun toString(): String = "Policy.$scope.AllowTypeCoercion"
        }
    }

    data class DataRepresentation(
        val use_cdrv0_representation: Boolean = false,
        val use_xcdrv2_representation: Boolean = false,
    ) : TypeConsistency()

    data class EntityName(val name: String) : TypeConsistency()

}

open class Qos(
    policies: ArrayList<Policy.BasePolicy>? = null,
    qos: Long = 0,
    forEntity: String = ""
) {
    var qos: Long
    var policies: ArrayList<Policy.BasePolicy>?
    var forEntry: String = "ALLQos"

    init {
        this.policies = policies
        if (this is DomainParticipantQos) {
            _assert_consistency(policies, DomainParticipantQos.supportedScopes)
        } else if (this is TopicQos) {
            _assert_consistency(policies, TopicQos.supportedScopes)
        } else if (this is PublisherQos) {
            _assert_consistency(policies, PublisherQos.supportedScopes)
        } else if (this is SubscriberQos) {
            _assert_consistency(policies, SubscriberQos.supportedScopes)
        } else if (this is DataWriterQos) {
            _assert_consistency(policies, DataWriterQos.supportedScopes)
        } else if (this is DataReaderQos) {
            _assert_consistency(policies, DataReaderQos.supportedScopes)
        }
        this.qos = _CQos.qos_to_cqos(policies)
    }

    fun get_qos(): Long {
        return qos
    }

    private fun _assert_consistency(
        policies: ArrayList<Policy.BasePolicy>?,
        supportedscopes: Set<String>
    ) {
        if (policies != null) {
            policies.forEach {
                val isExist = supportedscopes.find { supportedScope ->
                    supportedScope == it.scope
                }
                if (isExist == null) {
                    throw Exception("$forEntry qos error")
                }
            }
        }
    }
}

open class LimitedScopeQos(policies: ArrayList<Policy.BasePolicy>? = null, qos: Long = 0) :
    Qos(policies, qos) {
    open val forEntity: String = ""
//    open val supportedScopes: Set<String> = emptySet()
//    fun _assert_consistency(policies:ArrayList<Policy.BasePolicy>?, supportedscopes: Set<String>){
//        if(policies!=null){
//
//        }
//    }

}


class DomainParticipantQos(policies: ArrayList<Policy.BasePolicy>) : Qos(policies) {
    //    val forEntity: String = "DomainParticipant"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Property",
            "Userdata",
            "IgnoreLocal"
        )
    }
//    init {
//        qos = _CQos.qos_to_cqos(policies)
//
//    }
}

class TopicQos(policies: ArrayList<Policy.BasePolicy>?) : Qos(policies) {
    //    override val forEntity: String = "Topic"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Deadline",
            "DestinationOrder",
            "Durability",
            "DurabilityService",
            "History",
            "IgnoreLocal",
            "LatencyBudget",
            "Lifespan",
            "Liveliness",
            "Ownership",
            "Property",
            "Reliability",
            "ResourceLimits",
            "Topicdata",
            "TransportPriority",
            "TypeConsistency",
            "DataRepresentation"
        )
    }
//    override fun _assert_consistency(policies: ArrayList<Policy.BasePolicy>?) {
//        if(policies!=null)
//            policies.forEach {
//                val isExist = TopicQos.supportedScopes.find { supportedScope ->
//                    supportedScope == it.scope
//                }
//                if(isExist == null){
//                    throw Exception("ERROR QOS")
//                }
//            }
//    }
//
//    init {
//        _assert_consistency(policies)
//        super.qos = _CQos.qos_to_cqos(policies)
//    }
}

class PublisherQos(policies: ArrayList<Policy.BasePolicy>) : Qos(policies) {
    //    override val forEntity: String = "Publisher"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Groupdata",
            "IgnoreLocal",
            "Partition",
            "PresentationAccessScope",
            "Property"
        )
    }
//    override fun _assert_consistency(policies: ArrayList<Policy.BasePolicy>?) {
//        if(policies!=null)
//            policies.forEach {
//                val isExist = PublisherQos.supportedScopes.find { supportedScope ->
//                    supportedScope == it.scope
//                }
//                if(isExist == null){
//                    throw Exception("ERROR QOS")
//                }
//            }
//    }
//
//    init {
//        _assert_consistency(policies)
//        super.qos = _CQos.qos_to_cqos(policies)
//    }
}

class SubscriberQos(policies: ArrayList<Policy.BasePolicy>?) : Qos(policies) {
    //    override val forEntity: String = "Subscriber"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Groupdata",
            "IgnoreLocal",
            "Partition",
            "PresentationAccessScope",
            "Property"
        )
    }
//    override fun _assert_consistency(policies: ArrayList<Policy.BasePolicy>?, ) {
//        if(policies!=null)
//            policies.forEach {
//                val isExist = SubscriberQos.supportedScopes.find { supportedScope ->
//                    supportedScope == it.scope
//                }
//                if(isExist == null){
//                    throw Exception("ERROR QOS")
//                }
//            }
//    }

//    init {
//        _assert_consistency(policies)
//        super.qos = _CQos.qos_to_cqos(policies)
//    }
}


class DataWriterQos(policies: ArrayList<Policy.BasePolicy>?) : Qos(policies) {
    //    override val forEntity: String = "DataWriter"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Deadline",
            "DestinationOrder",
            "Durability",
            "DurabilityService",
            "History",
            "IgnoreLocal",
            "LatencyBudget",
            "Lifespan",
            "Liveliness",
            "Ownership",
            "OwnershipStrength",
            "Property",
            "Reliability",
            "ResourceLimits",
            "TransportPriority",
            "Userdata",
            "WriterDataLifecycle",
            "TypeConsistency",
            "DataRepresentation"
        )
    }
//    override fun _assert_consistency(policies: ArrayList<Policy.BasePolicy>?) {
//        if(policies!=null)
//            policies.forEach {
//                val isExist = SubscriberQos.supportedScopes.find { supportedScope ->
//                    supportedScope == it.scope
//                }
//                if(isExist == null){
//                    throw Exception("ERROR QOS")
//                }
//            }
//    }
//
//    init {
//        _assert_consistency(policies)
//        _CQos.qos_to_cqos(policies)
//    }
}

class DataReaderQos(policies: ArrayList<Policy.BasePolicy>?) : Qos(policies) {
    //    override val forEntity: String = "DataWriter"
    companion object {
        val supportedScopes: Set<String> = setOf(
            "EntityName",
            "BinaryProperty",
            "Deadline",
            "DestinationOrder",
            "Durability",
            "DurabilityService",
            "History",
            "IgnoreLocal",
            "LatencyBudget",
            "Lifespan",
            "Liveliness",
            "Ownership",
            "OwnershipStrength",
            "Property",
            "Reliability",
            "ResourceLimits",
            "TransportPriority",
            "Userdata",
            "WriterDataLifecycle",
            "TypeConsistency",
            "DataRepresentation"
        )
    }
//    override fun _assert_consistency(policies: ArrayList<Policy.BasePolicy>?) {
//        if(policies!=null)
//            policies.forEach {
//                val isExist = SubscriberQos.supportedScopes.find { supportedScope ->
//                    supportedScope == it.scope
//                }
//                if(isExist == null){
//                    throw Exception("ERROR QOS")
//                }
//            }
//    }
//
//    init {
//        _assert_consistency(policies)
//    }
}


object _CQos {

    private fun _create_qos(): Long {
        return kScomCreateQos()
    }

    val supportScopes: Set<String> = setOf(
        "Reliability", "Durability", "History", "ResourceLimits", "PresentationAccessScope",
        "Lifespan", "Deadline", "LatencyBudget", "Ownership", "OwnershipStrength",
        "Liveliness", "TimeBasedFilter", "Partition", "TransportPriority",
        "DestinationOrder", "WriterDataLifecycle", "ReaderDataLifecycle",
        "DurabilityService", "IgnoreLocal", "Userdata", "Groupdata", "Topicdata",
        "Property", "BinaryProperty", "TypeConsistency", "DataRepresentation",
        "EntityName"
    )


    fun qos_to_cqos(policies: ArrayList<Policy.BasePolicy>?): Long {
        if (policies == null) {
            return 0L
        }
        val cqos = kScomCreateQos()

        policies.forEach {
            val isExist = supportScopes.find { scopes ->
                scopes == it.scope
            }
            if (isExist == null) {
                throw Exception("ERROR QOS")
            }

            val functionName: String = ("_set_kscom_" + it.scope).lowercase()
            _set_maps[functionName]?.invoke(cqos, it)
        }
        return cqos
    }

    fun cqos_to_qos(cqos: Long): ArrayList<Policy.BasePolicy>? {
        if (cqos == 0L) {
            return null
        }
        val qosClass = _CQos
        val Policies: ArrayList<Policy.BasePolicy> = ArrayList()
        supportScopes.forEach {
            val functionName: String = ("_get_kscom_" + it.toString().lowercase())
            val policy = _get_maps[functionName]?.invoke(cqos)
            if (policy != null) {
                if (policy is Policy.BasePolicy) {
                    Policies.add(policy)
                }
                if (policy is ArrayList<*>) {
                    policy.forEach {
                        if (it is Policy.BasePolicy) {
                            Policies.add(it)
                        }
                    }
                }
            }


        }
        return Policies
    }

    private val _set_maps: Map<String, (cqos: Long, policy: Policy.BasePolicy) -> Unit> =
        mutableMapOf(
            "_set_kscom_reliability" to _CQos::_set_kscom_reliability,
            "_set_kscom_durability" to _CQos::_set_kscom_durability,
            "_set_kscom_resourcelimits" to _CQos::_set_kscom_resourcelimits,
            "_set_kscom_presentationaccessscope" to _CQos::_set_kscom_presentationaccessscope,
            "_set_kscom_lifespan" to _CQos::_set_kscom_lifespan,
            "_set_kscom_deadline" to _CQos::_set_kscom_deadline,
            "_set_kscom_latencybudget" to _CQos::_set_kscom_latencybudget,
            "_set_kscom_ownership" to _CQos::_set_kscom_ownership,
            "_set_kscom_ownershipstrength" to _CQos::_set_kscom_ownershipstrength,
            "_set_kscom_liveliness" to _CQos::_set_kscom_liveliness,
            "_set_kscom_timebasedfilter" to _CQos::_set_kscom_timebasedfilter,
            "_set_kscom_partition" to _CQos::_set_kscom_partition,
            "_set_kscom_transportpriority" to _CQos::_set_kscom_transportpriority,
            "_set_kscom_destinationorder" to _CQos::_set_kscom_destinationorder,
            "_set_kscom_writerdatalifecycle" to _CQos::_set_kscom_writerdatalifecycle,
            "_set_kscom_readerdatalifecycle" to _CQos::_set_kscom_readerdatalifecycle,
            "_set_kscom_durabilityservies" to _CQos::_set_kscom_durabilityservies,
            "_set_kscom_ignorelocal" to _CQos::_set_kscom_ignorelocal,
            "_set_kscom_userdata" to _CQos::_set_kscom_userdata,
            "_set_kscom_topicdata" to _CQos::_set_kscom_topicdata,
            "_set_kscom_groupdata" to _CQos::_set_kscom_groupdata,
            "_set_kscom_property" to _CQos::_set_kscom_property,
            "_set_kscom_binaryproperty" to _CQos::_set_kscom_binaryproperty,
            "_set_kscom_typeconsistency" to _CQos::_set_kscom_typeconsistency,
            "_set_kscom_datarepresentation" to _CQos::_set_kscom_datarepresentation,
            "_set_kscom_entityname" to _CQos::_set_kscom_entityname,
        )

    private val _get_maps: Map<String, (cqos: Long) -> Any> = mutableMapOf(
        "_get_kscom_reliability" to _CQos::_get_kscom_reliability,
        "_get_kscom_durability" to _CQos::_get_kscom_durability,
        "_get_kscom_resourcelimits" to _CQos::_get_kscom_resourcelimits,
        "_get_kscom_presentationaccessscope" to _CQos::_get_kscom_presentationaccessscope,
        "_get_kscom_lifespan" to _CQos::_get_kscom_lifespan,
        "_get_kscom_deadline" to _CQos::_get_kscom_deadline,
        "_get_kscom_latencybudget" to _CQos::_get_kscom_latencybudget,
        "_get_kscom_ownership" to _CQos::_get_kscom_ownership,
        "_get_kscom_ownershipstrength" to _CQos::_get_kscom_ownershipstrength,
        "_get_kscom_liveliness" to _CQos::_get_kscom_liveliness,
        "_get_kscom_timebasedfilter" to _CQos::_get_kscom_timebasedfilter,
        "_get_kscom_partition" to _CQos::_get_kscom_partition,
        "_get_kscom_transportpriority" to _CQos::_get_kscom_transportpriority,
        "_get_kscom_destinationorder" to _CQos::_get_kscom_destinationorder,
        "_get_kscom_writerdatalifecycle" to _CQos::_get_kscom_writerdatalifecycle,
        "_get_kscom_readerdatalifecycle" to _CQos::_get_kscom_readerdatalifecycle,
        "_get_kscom_durabilityservies" to _CQos::_get_kscom_durabilityservies,
        "_get_kscom_ignorelocal" to _CQos::_get_kscom_ignorelocal,
        "_get_kscom_userdata" to _CQos::_get_kscom_userdata,
        "_get_kscom_topicdata" to _CQos::_get_kscom_topicdata,
        "_get_kscom_groupdata" to _CQos::_get_kscom_groupdata,
        "_get_kscom_property" to _CQos::_get_kscom_property,
        "_get_kscom_binaryproperty" to _CQos::_get_kscom_binaryproperty,
        "_get_kscom_typeconsistency" to _CQos::_get_kscom_typeconsistency,
        "_get_kscom_datarepresentation" to _CQos::_get_kscom_datarepresentation,
        "_get_kscom_entityname" to _CQos::_get_kscom_entityname,
    )


    private fun _set_kscom_reliability(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.Reliability.BestEffort" -> {
                kScomSetReliability(cqos, 0, 0)
            }

            "Policy.Reliability.Reliable" -> {
                if (policy is Policy.Reliability.Reliable) {
                    kScomSetReliability(cqos, 1, policy.maxBlockingTime)
                } else {
                    throw Exception("ERROR QOS")
                }

            }

            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_durability(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.Durability.Volatile" -> {
                kScomSetDurability(cqos, 0)
            }
            "Policy.Durability.TransientLocal" -> {
                kScomSetDurability(cqos, 1)
            }
            "Policy.Durability.Transient" -> {
                kScomSetDurability(cqos, 2)
            }
            else -> {
                kScomSetDurability(cqos, 3)
            }
        }
    }

    private fun _set_kscom_resourcelimits(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.ResourceLimits) {
            kScomSetResourceLimits(
                cqos,
                policy.max_samples,
                policy.max_instances,
                policy.max_instances
            )
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_presentationaccessscope(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.PresentationAccessScope.Instance" -> {
                if (policy is Policy.PresentationAccessScope.Instance)
                    kScomSetPresentation(cqos, 0, policy.coherent_access, policy.ordered_access)
            }

            "Policy.PresentationAccessScope.Topic" -> {
                if (policy is Policy.PresentationAccessScope.Topic)
                    kScomSetPresentation(cqos, 1, policy.coherent_access, policy.ordered_access)
            }

            "Policy.PresentationAccessScope.Group" -> {
                if (policy is Policy.PresentationAccessScope.Group)
                    kScomSetPresentation(cqos, 2, policy.coherent_access, policy.ordered_access)
            }

            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_lifespan(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.Lifespan) {
            kScomSetLifespan(cqos, policy.lifespan)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_deadline(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.Deadline) {
            kScomSetDeadline(cqos, policy.deadline)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_latencybudget(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.LatencyBudget) {
            kScomSetLatencyBudget(cqos, policy.budget)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_ownership(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.Ownership.Shared" -> kScomSetOwnership(cqos, 0)
            "Policy.Ownership.Exclusive" -> kScomSetOwnership(cqos, 1)
            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_ownershipstrength(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.OwnershipStrength) {
            kScomSetOwnershipStrength(cqos, policy.strength)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_liveliness(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.Liveliness.Automatic" -> {
                if (policy is Policy.Liveliness.Automatic)
                    kScomSetLiveliness(cqos, 0, policy.lease_duration)
            }

            "Policy.Liveliness.ManualByParticipant" -> {
                if (policy is Policy.Liveliness.ManualByParticipant)
                    kScomSetLiveliness(cqos, 1, policy.lease_duration)
            }

            "Policy.Liveliness.ManualByTopic" -> {
                if (policy is Policy.Liveliness.ManualByTopic)
                    kScomSetLiveliness(cqos, 2, policy.lease_duration)
            }

            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_timebasedfilter(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.TimeBasedFilter) {
            kScomSetTimeBasedFilter(cqos, policy.filter_time)
        }
    }

    private fun _set_kscom_partition(cqos: Long, policy: BasePolicy) {
        //todo
    }

    private fun _set_kscom_transportpriority(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.TransportPriority) {
            kScomSetTransportPriority(cqos, policy.priority)
        }
    }

    private fun _set_kscom_destinationorder(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.DestinationOrder.ByReceptionTimestamp" -> {
                kScomSetDestinationOrder(cqos, 0)
            }

            "Policy.DestinationOrder.BySourceTimestamp" -> {
                kScomSetDestinationOrder(cqos, 1)
            }

            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_writerdatalifecycle(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.WriteDataLifecycle) {
            kScomSetWriterDataLifecycle(cqos, policy.autodispose)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_readerdatalifecycle(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.ReaderDataLifecycle) {
            kScomSetReaderDataLifecycle(
                cqos,
                policy.autopurge_nowriter_samples_delay,
                policy.autopurge_disposed_samples_delay
            )
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_durabilityservies(cqos: Long, policy: Policy.BasePolicy) {
        if (policy is Policy.DurabilityService) {
            when (policy.history.toString()) {
                "Policy.History.KeepAll" -> {
                    kScomSetDurabilityservice(
                        cqos,
                        policy.cleanup_delay,
                        1,
                        0,
                        policy.max_samples,
                        policy.max_instances,
                        policy.max_samples_per_instance
                    )
                }

                "Policy.History.KeepLast" -> {
                    if (policy.history is Policy.History.KeepLast) {
                        kScomSetDurabilityservice(
                            cqos,
                            policy.cleanup_delay,
                            0,
                            policy.history.depth,
                            policy.max_samples,
                            policy.max_instances,
                            policy.max_samples_per_instance
                        )
                    } else {
                        throw Exception("ERROR QOS")
                    }
                }

                else -> {
                    throw Exception("ERROR QOS")
                }
            }
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_ignorelocal(cqos: Long, policy: Policy.BasePolicy) {
        when (policy.toString()) {
            "Policy.IgnoreLocal.Nothing" -> {
                kScomSetIgnorelocal(cqos, 0)
            }

            "Policy.IgnoreLocal.Participant" -> {
                kScomSetIgnorelocal(cqos, 1)
            }

            "Policy.IgnoreLocal.Process" -> {
                kScomSetIgnorelocal(cqos, 2)
            }

            else -> {
                throw Exception("ERROR QOS")
            }
        }
    }

    private fun _set_kscom_userdata(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.Userdata) {
            kScomSetUserdata(cqos, policy.data, policy.data.size)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_topicdata(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.Topicdata) {
            kScomSetTopicdata(cqos, policy.data, policy.data.size)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_groupdata(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.Groupdata) {
            kScomSetGroupdata(cqos, policy.data, policy.data.size)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_property(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.Property) {
            kScomSetProperty(cqos, policy.key, policy.value)
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_binaryproperty(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.BinaryProperty) {
            kScomSetBinaryproperty(cqos, policy.key, policy.value, policy.value.size)
        } else {
            throw Exception("ERROR QOS")
        }

    }

    private fun _set_kscom_typeconsistency(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.TypeConsistency.DisallowTypeCoercion) {
            kScomSetTypeconsistency(
                cqos,
                0,
                false,
                false,
                false,
                false,
                policy.force_type_validation
            )
        } else if (policy is Policy.TypeConsistency.AllowTypeCoercion) {
            kScomSetTypeconsistency(
                cqos,
                1,
                policy.ignore_sequence_bounds,
                policy.ignore_string_bounds,
                policy.ignore_member_names,
                policy.prevent_type_widening,
                policy.force_type_validation
            )
        }
    }

    private fun _set_kscom_datarepresentation(cqos: Long, policy: BasePolicy) {
        if (policy is Policy.DataRepresentation) {
            if (policy.use_cdrv0_representation == true && policy.use_xcdrv2_representation == true) {
                kScomSetDataRepresentation(cqos, 0);
            }
            if (policy.use_cdrv0_representation == true) {
                kScomSetDataRepresentation(cqos, 1);
            }
            if (policy.use_xcdrv2_representation == true) {
                kScomSetDataRepresentation(cqos, 2);
            }
        } else {
            throw Exception("ERROR QOS")
        }
    }

    private fun _set_kscom_entityname(cqos: Long, policy: BasePolicy) { // need to test
        if (policy is Policy.EntityName) {
            kScomSetEntityName(cqos, policy.name)
        }
    }

    private fun _get_kscom_reliability(cqos: Long): Policy.Reliability {
        return kScomGetReliability(cqos)
    }

    private fun _get_kscom_durability(cqos: Long): Policy.Durability {
        return kScomGetDurability(cqos)
    }

    fun _get_kscom_resourcelimits(qosPtr: Long): Policy.ResourceLimits {
        return kScomGetResourceLimits(qosPtr)
    }

    fun _get_kscom_presentationaccessscope(qosPtr: Long): Policy.PresentationAccessScope {
        return kScomGetPresentation(qosPtr)
    }

    fun _get_kscom_lifespan(qosPtr: Long): Policy.Lifespan {
        return kScomGetLifespan(qosPtr)
    }

    fun _get_kscom_deadline(qosPtr: Long): Policy.Deadline {
        return kScomGetDeadline(qosPtr)
    }

    fun _get_kscom_latencybudget(qosPtr: Long): Policy.LatencyBudget {
        return kScomGetLatencyBudget(qosPtr)
    }

    fun _get_kscom_ownership(qosPtr: Long): Policy.Ownership {
        return kScomGetOwnership(qosPtr)
    }

    fun _get_kscom_ownershipstrength(qosPtr: Long): Policy.OwnershipStrength {
        return kScomGetOwnershipStrength(qosPtr)
    }

    fun _get_kscom_liveliness(qosPtr: Long): Policy.Liveliness {
        return kScomGetLiveliness(qosPtr)
    }

    fun _get_kscom_timebasedfilter(qosPtr: Long): Policy.TimeBasedFilter {
        return kScomGetTimeBasedFilter(qosPtr)
    }

    fun _get_kscom_partition(qosPtr: Long): Policy.Partition {
        return kScomGetPartition(qosPtr)
    }

    fun _get_kscom_transportpriority(qosPtr: Long): Policy.TransportPriority {
        return kScomGetTransportPriority(qosPtr)
    }

    fun _get_kscom_destinationorder(qosPtr: Long): Policy.DestinationOrder {
        return kScomGetDestinationOrder(qosPtr)
    }

    fun _get_kscom_writerdatalifecycle(qosPtr: Long): Policy.WriteDataLifecycle {
        return kScomGetWriteDataLifecycle(qosPtr)
    }

    fun _get_kscom_readerdatalifecycle(qosPtr: Long): Policy.ReaderDataLifecycle {
        return kScomGetReaderDataLifecycle(qosPtr)
    }

    fun _get_kscom_durabilityservies(qosPtr: Long): Policy.DurabilityService {
        return kScomGetDurabilityService(qosPtr)
    }

    fun _get_kscom_ignorelocal(qosPtr: Long): Policy.IgnoreLocal {
        return kScomGetIgnoreLocal(qosPtr)
    }

    fun _get_kscom_userdata(qosPtr: Long): Policy.Userdata {
        return kScomGetUserdata(qosPtr)
    }

    fun _get_kscom_topicdata(qosPtr: Long): Policy.Topicdata {
        return kScomGetTopicdata(qosPtr)
    }

    fun _get_kscom_groupdata(qosPtr: Long): Policy.Groupdata {
        return kScomGetGroupdata(qosPtr)
    }

    fun _get_kscom_property(qosPtr: Long): ArrayList<Policy.Property> {
        return kScomGetProperty(qosPtr)
    }

    fun _get_kscom_binaryproperty(qosPtr: Long): ArrayList<Policy.BinaryProperty> {
        return kScomGetBinaryProperty(qosPtr)
    }

    fun _get_kscom_typeconsistency(qosPtr: Long): Policy.TypeConsistency {
        return kScomGetTypeConsistency(qosPtr)
    }

    fun _get_kscom_datarepresentation(qosPtr: Long): Policy.DataRepresentation {
        return kScomGetDataRepresentation(qosPtr)
    }

    fun _get_kscom_entityname(qosPtr: Long): Policy.EntityName {
        return kScomGetEntityName(qosPtr)
    }


}