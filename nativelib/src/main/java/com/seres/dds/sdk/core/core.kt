package com.seres.dds.sdk.core

import com.seres.dds.sdk.Qos

// import android.util.Log

open class DDS {
    var ref: Int = 0

    constructor(id: Int) {
        ref = id
    }
}


class StatusMask {
    companion object {
        fun all(): UInt {
            return 0x80007fe7u
        }

        fun none(): UInt {
            return 0u
        }

        fun inconsistent_topic(): UInt {
            return 0x00000001u shl 0
        }

        fun offered_deadline_missed(): UInt {
            return 0x00000001u shl 1
        }

        fun requested_deadline_missed(): UInt {
            return 0x00000001u shl 2
        }

        fun offered_incompatible_qos(): UInt {
            return 0x00000001u shl 5
        }

        fun requested_incompatible_qos(): UInt {
            return 0x00000001u shl 6
        }

        fun sample_lost(): UInt {
            return 0x00000001u shl 7
        }

        fun sample_rejected(): UInt {
            return 0x00000001u shl 8
        }

        fun data_on_readers(): UInt {
            return 0x00000001u shl 9
        }

        fun data_available(): UInt {
            return 0x00000001u shl 10
        }

        fun liveliness_lost(): UInt {
            return 0x00000001u shl 11
        }

        fun liveliness_changed(): UInt {
            return 0x00000001u shl 12
        }

        fun publication_matched(): UInt {
            return 0x00000001u shl 13
        }

        fun subscription_matched(): UInt {
            return 0x00000001u shl 14
        }

        fun read_sample_state(): UInt {
            return 1u
        }

        fun read_not_read_sample_state(): UInt {
            return 2u
        }

        fun any_sample_state(): UInt {
            return 1u or 2u
        }

        fun new_view_state(): UInt {
            return 4u
        }

        fun not_new_view_state(): UInt {
            return 8u
        }

        fun any_view_state(): UInt {
            return 4u or 8u
        }

        fun alive_instance_state(): UInt {
            return 16u
        }

        fun not_alive_disposed_instance_state(): UInt {
            return 32u
        }

        fun not_alive_no_writers_instance_state(): UInt {
            return 64u
        }

        fun any_intance_state(): UInt {
            return 16u or 32u or 64u
        }

        fun any_state(): UInt {
            return any_intance_state() or any_view_state() or any_sample_state()
        }
    }
}


open class Entity(entityid: Int) : DDS(entityid) {
    companion object {
        private const val TAG = "Entity"
    }
    var entities = mutableMapOf<Int, Entity>()
    private var qos: Qos? = null

    protected fun setqos(qos : Qos?){
        this.qos = qos
    }

    fun setref(entityid: Int) {
        super.ref = entityid
    }

    // fun get_subscriber(){}

    // fun get_publisher(){}

    // fun get_datareader(){}

    // fun get_guid(){}

    // fun read_status(){}

    // fun take_status(){}

    // fun get_status_changes(){}

    // fun get_status_mask(){}

    // fun set_status_mask(){}

    fun get_qos(): Long {
        return qos?.get_qos() ?: 0
    }

     fun set_qos(){

     }

    // fun get_listener(){}

    // fun set_listener(){}

    // fun get_parent(){}

    // fun get_participant(){}

    // fun get_children(){}

    // fun get_domain_id(){}

}

fun duration(
    weeks: Long = 0, days: Long = 0, hours: Long = 0, minutes: Long = 0, seconds: Long = 0,
    milliseconds: Long = 0, microseconds: Long = 0, nanoseconds: Long = 0, infinite: Boolean = false
): Long {
    if (infinite) {
        return 0x7FFFFFFF
    }

    var dayret: Long = days + weeks * 7
    var hourret: Long = dayret * 24 + hours
    var minuteret: Long = hourret * 60 + minutes
    var secondret: Long = minuteret * 60 + seconds
    var millisecondret: Long = secondret * 1000 + milliseconds
    var microsecondret: Long = millisecondret * 1000 + microseconds
    var nanosecondret: Long = microsecondret * 1000 + nanoseconds

    return nanosecondret
}

enum class ViewState(var value: UInt) {
    New(4U),
    Old(8U),
    Any(12U)
}

enum class InstanceState(var value: UInt) {
    Alive(16U),
    NotAliveDisposed(32U),
    NotAliveNoWriters(64U),
    Any(112U)
}

enum class SampleState(var value: UInt) {
    Read(1U),
    NotRead(2U),
    Any(3U)
}

enum class DDSStatus(var value: Int) {
    InconsistentTopic(1 shl 0),
    OfferedDeadlineMissed(1 shl 1),
    RequestedDeadlineMissed(1 shl 2),
    OfferedIncompatibleQos(1 shl 3),
    RequestedIncompatibleQos(1 shl 4),
    SampleLost(1 shl 5),
    SampleRejected(1 shl 6),
    DataOnReaders(1 shl 7),
    DataAvailable(1 shl 8),
    LivelinessLost(1 shl 9),
    LivelinessChanged(1 shl 10),
    PublicationMatched(1 shl 11), // 1000 0000 0000
    SubscriptionMatched(1 shl 12), // 10000 0000 0000
    All((1 shl 13) - 1), // 1 1111 1111 1111
}