package com.seres.dds.sdk.idl

import kotlin.reflect.*
import kotlin.reflect.full.*
import kotlin.reflect.jvm.isAccessible



class Builder {
    companion object {

        fun create_machine(ukclass: KClass<*>?, property: KProperty1<*, *>, optional: Bo<PERSON>an, length:Int):Machine{
            property.isAccessible = true
            val classifier = property.returnType.classifier

            when (classifier) {
                is KClass<*> -> {
                    when {
                        classifier.isSubclassOf(String::class)  -> return StringMachine(property.name, optional, length)
                        classifier.isSubclassOf(Int::class)     -> return PrimitiveMachine(property.name, PrimitiveType.Int,     4, optional, length)
                        classifier.isSubclassOf(UInt::class)    -> return PrimitiveMachine(property.name, PrimitiveType.UInt,    4, optional, length)
                        classifier.isSubclassOf(Long::class)    -> return PrimitiveMachine(property.name, PrimitiveType.Long,    8, optional, length)
                        classifier.isSubclassOf(ULong::class)   -> return PrimitiveMachine(property.name, PrimitiveType.ULong,   8, optional, length)
                        classifier.isSubclassOf(Short::class)   -> return PrimitiveMachine(property.name, PrimitiveType.Short,   2, optional, length)
                        classifier.isSubclassOf(UShort::class)  -> return PrimitiveMachine(property.name, PrimitiveType.UShort,  2, optional, length)
                        classifier.isSubclassOf(Byte::class)    -> return PrimitiveMachine(property.name, PrimitiveType.Byte,    1, optional, length)
                        classifier.isSubclassOf(UByte::class)   -> return PrimitiveMachine(property.name, PrimitiveType.UByte,   1, optional, length)
                        classifier.isSubclassOf(Boolean::class) -> return PrimitiveMachine(property.name, PrimitiveType.Boolean, 1, optional, length)
                        classifier.isSubclassOf(Float::class)   -> return PrimitiveMachine(property.name, PrimitiveType.Float,   4, optional, length)
                        classifier.isSubclassOf(Double::class)  -> return PrimitiveMachine(property.name, PrimitiveType.Double,  8, optional, length)
                        classifier.isSubclassOf(TypeStruct::class)-> return StructMachine(property.name, classifier, optional, length)
                        classifier.isSubclassOf(TypeUnion::class) -> return UnionMachine(property.name, ukclass!!, optional, length)
                        classifier.isSubclassOf(ArrayList::class) -> {
                            val arrayElementType = property.returnType.arguments.firstOrNull()?.type?.classifier as? KClass<*> ?: throw IllegalArgumentException("Unable to determine element type")
                            return createArrayMachine(property.name, arrayElementType, optional, length)
                        }
                        classifier.isSubclassOf(ArrayDeque::class) -> {
                            val sequenceElementType = property.returnType.arguments.firstOrNull()?.type?.classifier as? KClass<*> ?: throw IllegalArgumentException("Unable to determine element type")
                            return createSequenceMachine(property.name, sequenceElementType, optional, length)
                        }

                        else -> throw IllegalArgumentException("Unsupported type")
                    }
                }
                else -> throw IllegalArgumentException("Unsupported classifier")
            } 
        }

        fun createArrayMachine(name: String, elementType: KClass<*>, optional: Boolean, length: Int): ArrayListMachine{

            when {
                elementType.isSubclassOf(Int::class)     -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Int,     4, false, length), optional, length)
                elementType.isSubclassOf(UInt::class)    -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.UInt,    4, false, length), optional, length)
                elementType.isSubclassOf(Long::class)    -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Long,    8, false, length), optional, length)
                elementType.isSubclassOf(ULong::class)   -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.ULong,   8, false, length), optional, length)
                elementType.isSubclassOf(Short::class)   -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Short,   2, false, length), optional, length)
                elementType.isSubclassOf(UShort::class)  -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.UShort,  2, false, length), optional, length)
                elementType.isSubclassOf(Byte::class)    -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Byte,    1, false, length), optional, length)
                elementType.isSubclassOf(UByte::class)   -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.UByte,   1, false, length), optional, length)
                elementType.isSubclassOf(Boolean::class) -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Boolean, 1, false, length), optional, length)
                elementType.isSubclassOf(Float::class)   -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Float,   4, false, length), optional, length)
                elementType.isSubclassOf(Double::class)  -> return ArrayListMachine(name, PrimitiveMachine(name, PrimitiveType.Double,  8, false, length), optional, length)
                elementType.isSubclassOf(TypeStruct::class)-> return ArrayListMachine(name, StructMachine(name, elementType, false, length), optional, length)
                else -> throw IllegalArgumentException("Unsupported type")
            }
        }

        fun createSequenceMachine(name: String, elementType: KClass<*>, optional: Boolean, length: Int): SequenceMachine{
            when {
                elementType.isSubclassOf(Int::class)     -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Int,     4, false, length), optional, length)
                elementType.isSubclassOf(UInt::class)    -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.UInt,    4, false, length), optional, length)
                elementType.isSubclassOf(Long::class)    -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Long,    8, false, length), optional, length)
                elementType.isSubclassOf(ULong::class)   -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.ULong,   8, false, length), optional, length)
                elementType.isSubclassOf(Short::class)   -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Short,   2, false, length), optional, length)
                elementType.isSubclassOf(UShort::class)  -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.UShort,  2, false, length), optional, length)
                elementType.isSubclassOf(Byte::class)    -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Byte,    1, false, length), optional, length)
                elementType.isSubclassOf(UByte::class)   -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.UByte,   1, false, length), optional, length)
                elementType.isSubclassOf(Boolean::class) -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Boolean, 1, false, length), optional, length)
                elementType.isSubclassOf(Float::class)   -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Float,   4, false, length), optional, length)
                elementType.isSubclassOf(Double::class)  -> return SequenceMachine(name, PrimitiveMachine(name, PrimitiveType.Double,  8, false, length), optional, length)
                elementType.isSubclassOf(TypeStruct::class)-> return SequenceMachine(name, StructMachine(name, elementType, false, length), optional, length)
                else -> throw IllegalArgumentException("Unsupported type")
            }
        }
    }
}