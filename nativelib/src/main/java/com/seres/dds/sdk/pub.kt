package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.KScomNativeLib.KScomDDSWrite
import com.seres.dds.sdk.core.Entity
import com.seres.dds.sdk.idl.TypeBase

// import com.seres.dds.sdk.core.Listener


class Publisher : Entity {

    constructor(participant: DomainParticipant) : super(
        KScomNativeLib.kScomCreatePublisher(
            participant.ref,
            null,
            null
        )
    )

    fun suspend() {
        KScomNativeLib.kScomSuspend(super.ref)
    }

    fun resume() {
        KScomNativeLib.kScomResume(super.ref)
    }

    fun wait_for_acks(timeout: Long): <PERSON><PERSON>an {
        val ret: Int = KScomNativeLib.kScomWaitForAcks(super.ref, timeout)

        return if (ret == 0) {
            true
        } else {
            false
        }
    }
}


class DataWriter : Entity {
    var _topic: Topic? = null
    var _qos: Qos? = null

    companion object {
        const val TAG = "DataWriter"

        private fun getEntityId(par_or_pub: Entity, topic: Topic, qos: Qos? = null): Int {
            // 获取qos
            return KScomNativeLib.kScomCreateWriter(
                par_or_pub.ref,
                topic.ref,
                qos,
                null
            )
        }
    }

    constructor(par_or_pub: Entity, topic: Topic, qos: Qos? = null) : super(getEntityId(par_or_pub, topic, qos)) {
        _topic = topic
        _qos = qos
    }


    fun topic(): Topic {
        return _topic!!
    }

//    fun listener(listener: Listener, mask: UInt) {
//        super.listener_set(listener, mask, true)
//    }

    fun write(sample: TypeBase, timestamp: Int = 0): Int {
        sample.serialize(sample.getbuffer())

        var container: SampleContainerT = SampleContainerT()

        container.usample = sample.getbuffer().get_bytes()
        container.usample_size = container.usample!!.size.toLong()

        val retcode = KScomDDSWrite(ref, container)

        println(TAG + "ddswrite recode ${retcode}")
        return retcode
    }

    /**
     * 通过JNI调用cyclonedds中的dds.h
     * dds_get_status_changes(dds_entity_t entity, uint32_t *status)方法实现获取DataWriter/DataReader的状态码
     *
     * @return
     */
    fun get_status_changes(): Int {
        val entityId = ref
        val status = KScomNativeLib.kScomGetStatusChanges(entityId)
        println(TAG + "Get DataWriter entityId=$entityId status change=$status")
        return status
    }
}