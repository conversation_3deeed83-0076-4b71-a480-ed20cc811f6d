package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.duration
import com.seres.dds.sdk.idl.TypeBase

open class ReplierParams {
    private var participant: DomainParticipant
    private var max_wait: Long
    private var service_name: String
    private var wr_qos:Qos?
    private var rd_qos:Qos?

    constructor(par: DomainParticipant, wrQos: Qos?, rdQos: Qos?) {
        participant = par
        max_wait = duration(seconds = 3)
        service_name = ""
        wr_qos = wrQos
        rd_qos = rdQos
    }

    fun get_participant(): DomainParticipant {
        return participant
    }

    fun set_participant(par: DomainParticipant) {
        participant = par
    }

    fun get_service_name(): String {
        return service_name
    }

    fun set_service_name(name: String) {
        service_name = name
    }

    fun get_wr_qos():Qos?{
        return wr_qos
    }

    fun set_wr_qos(qos:Qos){
        wr_qos = qos
    }

    fun get_rd_qos():Qos?{
        return rd_qos
    }

    fun set_rd_qos(qos:Qos){
        rd_qos = qos
    }
}

class Replier {
    private var participant: DomainParticipant
    private var writer: DataWriter
    private var reader: DataReader
    private var service_name: String
    // private var waitset:Waitset

    constructor(params: ReplierParams, reqtype: TypeBase, reptype: TypeBase) {
        participant = params.get_participant()
        service_name = params.get_service_name()

        var replytp = Topic(participant, service_name + "Reply", reptype)
        writer = DataWriter(participant, replytp, params.get_wr_qos())
        println(TAG + "replier requesttp ${replytp.ref}")
        println(TAG + "replier writer ${writer.ref}")

        var requesttp = Topic(participant, service_name + "Request", reqtype)
        reader = DataReader(participant, requesttp, params.get_rd_qos())
        println(TAG + "Replier constructor serviceName=$service_name,replyType=${replytp.get_dataType().gettypename()},requestType=${requesttp.get_dataType().gettypename()}")
        println(TAG + "replier requesttp ${requesttp.ref}")
        println(TAG + "replier reader ${reader.ref}")
//        println(TAG + "Replier constructor serviceName=$service_name,replyType=${replytp.get_dataType().typename},requestType=${requesttp.get_dataType().typename}")
        //create readcondition
        //create waitset
    }

    fun send_reply(reply: TypeBase): Int {
        println(TAG + "send reply start")
        val retcode = writer.write(reply)
        println(TAG + "send reply write retcode = ${retcode}")
        return retcode
    }

    fun receive_request(): TypeBase? {
        val samples = reader.take()
//        var res: ArrayList<TypeBase>? = null
        var res : TypeBase? = null
        samples.sample_list?.forEach { sample ->
            if (sample.info?.valid_data == true) {
                println(TAG + "replier match valid request sample")
//                res!!.add(sample.type)
                res = sample.type
            }
        }
        return res
    }

    fun get_reader(): DataReader {
        return reader
    }

    fun get_writer(): DataWriter {
        return writer
    }

    companion object{
        private const val TAG = "Replier"
    }
}

