package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.InstanceState
import com.seres.dds.sdk.core.SampleState
import com.seres.dds.sdk.core.ViewState
import com.seres.dds.sdk.idl.TypeBase

abstract class Dispatcher<InterfaceType>(
    var requestType: TypeBase,
    var replyType: TypeBase
) {
    abstract fun process(request: TypeBase?)
    abstract fun add_service_impl(serviceImpl: InterfaceType)
    abstract fun set_replier(replier: Replier)
}

open class ServiceEndpoint<InterfaceType>(
    var serviceImpl: InterfaceType,
    var param: ServiceParam,
    var server: Server,
    var dispatcher: Dispatcher<InterfaceType>
) {
    private val replier: Replier

    init {
        this.replier = Replier(param, dispatcher.requestType, dispatcher.replyType)
        this.dispatcher.add_service_impl(serviceImpl)
        this.dispatcher.set_replier(replier)

        val rc = ReadCondition(
            replier.get_reader(),
            ViewState.Any.value.or(InstanceState.Any.value).or(SampleState.Any.value)
        )
        val attachRet = this.server.waitSet().attach(rc)
        println(TAG + "waitSet.attachRet=${attachRet}, readCondition mask=${rc._mask}, ref=${rc.ref}")
        server.appendService(this)
    }

    fun reply_datawriter(): DataWriter {
        return replier.get_writer()
    }

    fun request_datareader(): DataReader {
        return replier.get_reader()
    }

//    @SuppressLint("SuspiciousIndentation")
    fun dispatch_request() {
        var type : TypeBase? = null
        type  = replier.receive_request()
//        typearr?.forEach{sample->
        dispatcher.process(type)
//        }
    }

    companion object {
        private const val TAG = "ServiceEndpoint"
    }
}