package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.Entity
import com.seres.dds.sdk.idl.GetMaskResp
import com.seres.dds.sdk.idl.ReadGuardConditionResp
import com.seres.dds.sdk.idl.TakeGuardConditionResp

open class Condition : Entity {

    constructor(entityid: Int) : super(entityid) {
        println(TAG + "condition : ${entityid}")
    }

    fun get_mask(): Int {
        var ret = _get_mask(super.ref)

        if (ret.ret == 0) {
            val ret_mask = ret.mask
            return ret_mask
        }else{
            //exception
        }
        return 0
    }

    fun is_triggered(): Boolean {
        val ret = _triggered(super.ref)
        check(ret >= 0) { "Occurred when checking if  ${super.ref} was triggered" }
        return ret == 1
    }

    fun _get_mask(entityid: Int): GetMaskResp {
        return KScomNativeLib.kScomGetMask(entityid)
    }

    fun _triggered(entityid: Int): Int {
        return KScomNativeLib.kScomTriggered(entityid)
    }

    companion object {
        const val TAG = "DataWriter"
    }
}

class ReadCondition : Condition {
    var _reader: DataReader? = null
    var _mask: UInt = 0u

    constructor(reader: DataReader, mask: UInt) : super(
        KScomNativeLib.kScomCreateReadCondition(
            reader.ref,
            mask.toInt()
        )
    ) {

        this._reader = reader
        this._mask = mask
    }
}

/*
class QueryCondition : Condition{
    var _creator:()->TypeStruct
    var _reader : DataReader?=null
    var _mask : UInt = 0u
    // var filter
    val _call_back:CPointer<CFunction<(COpaquePointer?)->Boolean>>? = null
    fun call(samplePt : COpaquePointer):Boolean{

        val sampleInfoPointer: CPointer<CPointerVar<sample_info>>? = samplePt?.reinterpret<CPointerVar<sample_info>>()
        val sampleVar:CPointerVar<sample_info> = sampleInfoPointer!!.pointed
        val firstSample:sample_info? = sampleVar?.pointed
        val len1 :Int = firstSample!!.len.toInt()
        var byteArray = ByteArray(len1)
        // val firstSample.buf
        var voidPointer : COpaquePointer? = firstSample.buf
        // var charPtr = voidPointer?.reinterpret<CPointer<ByteVar>>()
        for(i in 0 until len1){
            byteArray[i] = voidPointer.readByte()
            voidPointer = voidPointer + 1
        }
        val buff : Buffer = Buffer(byteArray)
        var instance = _creator() as TypeStruct
        instance.deserialize(buff)


        // val sampleInfo : sample_info = firstSampleInfoPointer!!.pointed

        // val sampleInfo : CPointerVar<CPointer<sample_info>> = samplePt.reinterpret<CPointerVar<CPointer<sample_info>>>()

        // val samplePtptr : samplePt as  CPointerVar<CPointer<samplePt>>
        // val sampleInfo 



        // val sampleInfo = samplePt?.pointed
        // val arrayType = CArray<CByteVar>{sampleInfo?.len?:0}
        // val array = sampleInfo?.buf?.reinterpret<arrayType>()
        // val contents = array?.toList()?.toByteArray()
        // val des_data = this._reader._topic._data_type.deserialize(contents)
        return _call_back(instance)
    }
    constructor(reader:DataReader, mask:UInt, call_back:CPointer<CFunction<(COpaquePointer?)->Boolean>>,creator:()->TypeStruct):super(kscom_create_querycondition(reader.ref,mask,call)){
        _creator = creator
    }

    fun _create_querycondition(entityid : Int, mask : UInt,  call_back:CPointer<CFunction<(COpaquePointer?)->Boolean>>){
        return kscom_create_querycondition(entityid, mask, call_back)
    }


}
*/
class GuardCondition : Condition {
    var _domain_participant: DomainParticipant? = null

    constructor(domain_participant: DomainParticipant) : super(
        KScomNativeLib.kScomCreateGuardCondition(
            domain_participant.ref
        )
    ) {
        this._domain_participant = domain_participant
    }

    fun set(triggered: Boolean) {
        _set_guardcondition(super.ref, triggered)
    }

    fun read(): Boolean {
        val ret = _read_guardcondition(super.ref)
        check(ret.ret >= 0) { "Occurred when calling read on ${super.ref}" }
        return ret.triggered
    }

    fun take(): Boolean {
        var ret = _take_guardcondition(super.ref)
        check(ret.ret >= 0) { "Occurred when calling read on ${super.ref}" }
        return ret.triggered
    }

    fun _create_guardcondition(guardcond: Int): Int {
        return KScomNativeLib.kScomCreateGuardCondition(guardcond)
    }

    fun _set_guardcondition(guardcond: Int, triggered: Boolean): Int {
        return KScomNativeLib.kScomSetGuardCondition(guardcond, triggered)
    }

    fun _read_guardcondition(guardcond: Int): ReadGuardConditionResp {
        return KScomNativeLib.kScomReadGuardCondition(guardcond)
    }

    fun _take_guardcondition(guardcond: Int): TakeGuardConditionResp {
        return KScomNativeLib.kScomTakeGuardCondition(guardcond)
    }

}
