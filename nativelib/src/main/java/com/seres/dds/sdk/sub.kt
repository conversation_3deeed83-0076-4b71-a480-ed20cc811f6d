package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.Entity
import com.seres.dds.sdk.idl.Buffer
import com.seres.dds.sdk.idl.TypeBase

class Subscriber : Entity {

    constructor(participant: DomainParticipant,listener: Long? = null) : super(
        KScomNativeLib.kScomCreateSubscriber(
            participant.ref,
            0,
            if(listener != null) listener else 0
        )
    )

    fun notify_readers() {
        KScomNativeLib.kScomNotifyReaders(super.ref)
    }

}

class DataReader : Entity {
    var _topic: Topic? = null
    var _qos: Qos? = null

    constructor(par_or_sub: Entity, topic: Topic, qos: Qos? = null, listener: Long? = null) : super(
        KScomNativeLib.kScomCreateReader(
            par_or_sub.ref,
            topic.ref,
            qos,
            if(listener != null) listener else 0
        )
    ) {
        _topic = topic
        _qos  = qos
    }

    fun topic(): Topic {
        return _topic!!
    }

    fun take(N: Long = 1, condition: Entity? = null, instance_handle: Int? = null): Samples {

        val res = Samples()

        val rSampleContainerInfoPairs: ArrayList<Pair<SampleContainerT, SampleInfo>>? = KScomNativeLib.KScomDDSTake(ref, N)

        rSampleContainerInfoPairs?.apply {
            for (pair in rSampleContainerInfoPairs) {
                val sampleContainer = pair.first
                val buffer = Buffer(sampleContainer.usample)
                val de_res = _topic!!.get_dataType().deserialize(buffer)
                val sam = Sample()
                sam.type = de_res as TypeBase
                sam.info = pair.second
                res.add(sam)
                println(TAG + "de_res.typename ${de_res.gettypename()}")
                println(TAG + "sam.type.typename ${sam.type.gettypename()}")
            }
        } ?: apply {
            println(TAG + "JNI call return rsamples is null")
        }

        return res
    }

    fun get_status_changes(): Int {
        val entityId = ref
        val status = KScomNativeLib.kScomGetStatusChanges(entityId)
//        println(TAG + "Get DataReader entityId=$entityId status change=$status")
        return status
    }

    companion object {
        const val TAG = "sub"
    }
}