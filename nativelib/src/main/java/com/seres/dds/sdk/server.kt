package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.DDSStatus
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean

class Server{
    private val services : ArrayList<ServiceEndpoint<*>>
    private val waitSet :WaitSet
    private val threadPool: ExecutorService
//    private val condition:Condition
    constructor(param: ServerParam){
        services = ArrayList<ServiceEndpoint<*>>()
        waitSet = WaitSet(param.get_participant())
        threadPool = Executors.newFixedThreadPool(4)
//        condition = Condition(waitSet)
        waitSet.attach(waitSet)
    }
    @Volatile
    var state = AtomicBoolean(false)

    fun stop() {
        state.compareAndSet(true, false)
    }

    fun start() {
        state.compareAndSet(false, true)
        run()
    }

    fun waitSet(): WaitSet {
        return this.waitSet
    }

    fun appendService(service: ServiceEndpoint<*>) {
        if (services.contains(service)) {
            println(TAG + "already exist this service ${service.param.get_service_name()}")
        } else {
            services.add(service)
        }
    }

    private fun run() {
        while (state.get()) {
            val ret = this.waitSet.wait(TAG, Long.MAX_VALUE)
            println(TAG + "Server run wait ret=$ret")
            if (ret > 0) {
                println(TAG + "services count=${services.size}")
                for (service in services) {
                    if (service.request_datareader().get_status_changes().and(DDSStatus.DataAvailable.value) > 0) {
//                        threadPool.submit(object : Runnable {
//                            override fun run() {
//                                println(TAG + "server execute request task")
//                                service.dispatch_request()
//                            }
//                        })
                        service.dispatch_request()
                        println(TAG + "service do request success")
                    } else {
                        println(TAG + "service reader status abnormal")
                    }
                }
            } else {
                println(TAG + "server waitSet wait return=$ret")
            }
        }
    }

    companion object {
        private const val TAG = "Server"
    }
}