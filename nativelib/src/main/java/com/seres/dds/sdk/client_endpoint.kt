package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.DDSStatus
import com.seres.dds.sdk.idl.TypeBase

open class ClientEndpoint(
    param: ClientParam,
    requestType: TypeBase,
    replyType: TypeBase
) {
    private val requester: Requester
    private val maxWait: Long
//    private val entityPointGuid: EndpointGuid
    private val guid: ByteArray
    private var sequenceNumber: ULong
    private val guidPrfix : ByteArray
    private val entityKey : ByteArray
    private val entityKind : Byte

    init {
        this.requester = Requester(param, requestType, replyType)
        this.maxWait = param.getMaxWait()
        guid = KScomNativeLib.kScomGetGuid(requester.get_writer().ref)
//        this.guid = ByteArray(16)
        this.sequenceNumber = 0UL
        this.guidPrfix = guid.copyOf(12)
        this.entityKey = guid.copyOfRange(12,15)
        this.entityKind = guid.get(15).toByte()


//        val getGuid = KScomNativeLib.kScomGetGuid(requester.get_writer().ref)
//        if (getGuid == null) {
//            println(TAG + "JNI get guid null")
//        }
//        val guidPrefixArray = getGuid.copyOfRange(0, 12)
//        val entityKeyArray = getGuid.copyOfRange(12, 15)
//        val entityKindArray = getGuid.copyOfRange(15, 16)
//        this.entityPointGuid = EndpointGuid(guidPrefixArray, entityKeyArray, entityKindArray)
    }

    fun send_request(request: TypeBase) {
        requester.send_request(request)
    }

    fun receive_reply(): TypeBase? {
        if (!requester.wait_for_replies(maxWait)) {
            println(TAG + "[ClientEndpoint]--> wait_for_replies timeout maxWait=$maxWait")
            // return null
        }
        return requester.receive_reply()
    }

    /**
     * 客户端等待writer和reader状态就绪，5秒超时
     *
     * @param timeout
     * @return
     */
    fun wait_for_service(timeout: Long = 5000) {
        var remainWait = timeout
        val sleepTime = 100L
        println(TAG + "wait_for_service writer status=${requester.get_writer().get_status_changes()},reader status=${requester.get_reader().get_status_changes()}")
        while (requester.get_writer().get_status_changes().and(DDSStatus.PublicationMatched.value) <= 0 ||
            requester.get_reader().get_status_changes().and(DDSStatus.SubscriptionMatched.value) <= 0
        ) {
            println(TAG + "wait_for_service remainWait=$remainWait")
            if (remainWait <= 0) {
                throw Throwable("[ClientEndpoint]--> wait_for_service timeout")
            } else {
                remainWait -= sleepTime
            }
            Thread.sleep(sleepTime)
        }
    }

    fun reply_datareader(): DataReader {
        return requester.get_reader()
    }

    fun request_datawriter(): DataWriter {
        return requester.get_writer()
    }

    fun get_guidPrefix():ByteArray{
        return guidPrfix
    }
    fun get_entityKey():ByteArray{
        return entityKey
    }
    fun get_entityKind():Byte{
        return entityKind
    }
    fun get_sequenceNumber():ULong
    {
        return sequenceNumber
    }
    fun set_sequenceNumber(seq:ULong){
        sequenceNumber = seq
    }


    companion object {
        private const val TAG = "ClientEndpoint"
    }
}

/**
 * Endpoint guid
 * @property guidPrefix 12字节
 * @property entityKey 3字节
 * @property entityKind 1个字节
 * @constructor Create empty Endpoint guid
 */
//class EndpointGuid(
//    var guidPrefix: ByteArray,
//    var entityKey: ByteArray,
//    var entityKind: ByteArray
//)