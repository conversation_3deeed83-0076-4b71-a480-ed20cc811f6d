package com.seres.dds.sdk.idl

/**
 * 调用JNI接口kScomGetMask -> C接口kscom_take()的返回结果实体类
 *
 * @property ret
 * @property mask
 * @constructor Create empty Get mask resp
 */
data class GetMaskResp(var ret: Int, var mask: Int)

/**
 * 调用JNI接口kScomReadGuardCondition -> C接口kscom_read_guardcondition的返回结果实体类
 *
 * @property ret
 * @property triggered
 * @constructor Create empty Get read guard condition rsp
 */
data class ReadGuardConditionResp(var ret: Int, var triggered: Boolean)

/**
 * 调用JNI接口kScomTakeGuardCondition -> C接口kscom_take_guardcondition的返回结果实体类
 *
 * @property ret
 * @property triggered
 * @constructor Create empty Take guard condition resp
 */
data class TakeGuardConditionResp(var ret: Int, var triggered: Boolean)

/**
 * 调用JNI接口kScomWaitSetAttach -> C接口kscom_waitset_attach的返回结果实体类
 *
 * @property ret
 * @property value
 * @constructor Create empty Wait set attach resp
 */
data class WaitSetAttachResp(var ret: Int, var valueptr: Long)

/**
 * 调用JNI接口kScomWaitSetDetach -> C接口kscom_waitset_detach的返回结果实体类
 *
 * @property ret
 * @property value
 * @constructor Create empty Wait set detach
 */
data class WaitSetDetach(var ret: Int, var value: Int)

/**
 *  调用JNI接口kScomWaitSetWaitUntil -> C接口kscom_waitset_wait_until的返回结果实体类
 *
 * @property ret
 * @property value
 * @constructor Create empty Wait set wait until
 */
//data class WaitSetWaitUntil(var ret: Int, var value: Int)

