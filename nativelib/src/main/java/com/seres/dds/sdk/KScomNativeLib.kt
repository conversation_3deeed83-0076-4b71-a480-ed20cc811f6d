package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.InstanceState
import com.seres.dds.sdk.core.SampleState
import com.seres.dds.sdk.core.ViewState
import com.seres.dds.sdk.idl.GetMaskResp
import com.seres.dds.sdk.idl.ReadGuardConditionResp
import com.seres.dds.sdk.idl.TakeGuardConditionResp
import com.seres.dds.sdk.idl.WaitSetAttachResp
import com.seres.dds.sdk.idl.WaitSetDetach
//import com.seres.dds.sdk.idl.WaitSetWaitUntil

object KScomNativeLib {
    init {
        System.loadLibrary("kscom")
    }

    fun printLogFromJNI(msg: String) {
        println("KScomNativeLib" + msg)
    }

    external fun kScomCreateParticipant(domain: Int, qos: Long, listener: Long): Int

    external fun kScomCreatePublisher(participant: Int, qos: Long?, listener: Long?): Int

    external fun kScomSuspend(publisher: Int): Int

    external fun kScomResume(publisher: Int): Int

    external fun kScomWaitForAcks(publisherOrWriter: Int, timeout: Long): Int

    external fun kScomCreateWriter(
        participantOrPublisher: Int,
        topic: Int,
        qos: Qos?,
        listener: Long?
    ): Int

    external fun kScomCreateSubscriber(participant: Int, qos: Long, listener: Long): Int

    external fun kScomNotifyReaders(subscriber: Int): Int

    external fun kScomCreateReader(
        participantOrSubscriber: Int,
        topic: Int,
        qos: Qos?,
        listener: Long
    ): Int

    external fun kScomCreateTopic(
        participant: Int,
        topicName: String,
        desc: IdlTypeDescT,
        qos: Long,
        listener: Long
    ): Int

    external fun KScomDDSWrite(
        writer: Int,
        container: SampleContainerT
    ): Int

    external fun KScomDDSTake(reader: Int, N: Long): ArrayList<Pair<SampleContainerT, SampleInfo>>

    external fun kScomSetStatusMask(entity: Int, mask: Long): Int

    //todo 修改函数定义
    external fun kScomGetStatueMask(entity: Int): Long

    external fun kScomCreateWaitSet(owner: Int): Int

    external fun kScomWaitSetAttach(waitSet: Int, entity: Int): WaitSetAttachResp

    external fun kScomWaitSetDetach(waitSet: Int, entity: Int, valuePtr : Long): Int

    external fun kScomWaitSetWait(waitSet: Int, nxs: Long, relTimeout: Long): Int

    external fun kScomWaitSetWaitUntil(waitSet: Int, nxs: Long, absTimeout: Long): Int

    external fun kScomWaitSetSetTrigger(waitSet: Int, trigger: Boolean): Int

    /**** samples* kscom_take(dds_entity_t reader_or_condition, long long N); start****/
    external fun kScomTake(readerOrCondition: Int, N: Long): Long

    external fun getKdata(samplesPointer: Long): Long

    external fun getSample(kdataPointer: Long, index: Long): Long

    external fun getContainerUsampleSize(samplePointer: Long): Int

    external fun getContainerUsample(samplePointer: Long): Long

    external fun getSampleState(samplePointer: Long): Int

    external fun getNCount(samplesPointer: Long): Long

    /**** samples* kscom_take(dds_entity_t reader_or_condition, long long N); end****/

    external fun kScomGetMask(condition: Int): GetMaskResp

    external fun kScomTriggered(entityId: Int): Int

    external fun kScomCreateGuardCondition(owner: Int): Int

    external fun kScomSetGuardCondition(guardCond: Int, triggered: Boolean): Int

    external fun kScomReadGuardCondition(
        guardCond: Int
    ): ReadGuardConditionResp

    /**
     * 传入triggered参数示例
     * val triggered = BooleanArray(1) { false }
     */
    external fun kScomTakeGuardCondition(
        guardCond: Int
    ): TakeGuardConditionResp

    /**
     * 调用kscom_create_readcondition()
     *
     */
    external fun kScomCreateReadCondition(ref: Int, mask: Int): Int

    /**
     * dds_get_status_changes
     *
     * @param entity
     * @return
     */
    external fun kScomGetStatusChanges(entity: Int): Int

    external fun DDSCreateQos(): Qos

    external fun kScomGetGuid(ref: Int): ByteArray

    /**
     * 被JNI方法调用，将C中state值转换为SampleState
     *
     * @param state
     * @return
     */
    fun mapIntToSampleState(state: Int): SampleState {
        return when (state) {
            SampleState.Read.value.toInt() -> SampleState.Read // Java 中 SampleState.Read 的值为 1
            SampleState.NotRead.value.toInt() -> SampleState.NotRead // Java 中 SampleState.NotRead 的值为 2
            else -> return SampleState.Any  // Java 中 SampleState.Any 的值为 3
        }
    }

    /**
     * 被JNI方法调用，将C中state值转换为ViewState
     *
     * @param state
     * @return
     */
    fun mapIntToViewState(state: Int): ViewState {
        return when (state) {
            ViewState.New.value.toInt() -> ViewState.New // Java 中 ViewState.Read 的值为 4
            ViewState.Old.value.toInt() -> ViewState.Old // Java 中 ViewState.NotRead 的值为 8
            else -> return ViewState.Any  // Java 中 ViewState.Any 的值为 12
        }
    }

    /**
     * 被JNI方法调用，将C中state值转换为InstanceState
     *
     * @param state
     * @return
     */
    fun mapIntToInstanceState(state: Int): InstanceState {
        return when (state) {
            InstanceState.Alive.value.toInt() -> InstanceState.Alive // Java 中 InstanceState.Read 的值为 16
            InstanceState.NotAliveDisposed.value.toInt() -> InstanceState.NotAliveDisposed // Java 中 InstanceState.NotRead 的值为 32
            InstanceState.NotAliveNoWriters.value.toInt() -> InstanceState.NotAliveNoWriters // Java 中 InstanceState.NotRead 的值为 64
            else -> return InstanceState.Any  // Java 中 InstanceState.Any 的值为 128
        }
    }

    external fun kScomCreateListener(): Long

    external fun kScomLsetSubscriptionMatched(listener: Long, lobj:Listener, kind: Int)

    external fun kScomLsetOnDataAvailable(listener: Long, lobj:Listener, kind: Int)

    external fun kScomLsetPublicationMatched(listener: Long, lobj:Listener, kind: Int)

    external fun kScomCreateQos():Long

    external fun kScomDeleteQos(qosPtr : Long)

    external fun kScomSetDurability(qosPtr:Long, durabiliy_kind: Int)

    external fun kScomSetHistory(qosPtr: Long, history_kind: Int, depth: Int)

    external fun kScomSetResourceLimits(qosPtr: Long, max_samples : Int, max_instances: Int, max_samples_per_instance: Int)

    external fun kScomSetPresentation(qosPtr: Long, access_scope: Int, coherent_access : Boolean, ordered_access: Boolean)

    external fun kScomSetLifespan(qosPtr: Long,  lifespan: Long)

    external fun kScomSetDeadline(qosPtr: Long, deadline: Long)

    external fun kScomSetLatencyBudget(qosPtr: Long, duration: Long)

    external fun kScomSetOwnership(qosPtr: Long, kind: Int)

    external fun kScomSetOwnershipStrength(qosPtr: Long, value: Int)

    external fun kScomSetLiveliness(qosPtr: Long, kind: Int, lease_duration : Long)

    external fun kScomSetTimeBasedFilter(qosPtr: Long, minimum_separation:Long)

//    external fun kScomSetPartition(qosPtr: Long, n)


    external fun kScomSetReliability(qosPtr: Long, reliability_kind:Int,blocking_time:Long)

    external fun kScomSetTransportPriority(qosPtr: Long, value: Int)

    external fun kScomSetDestinationOrder(qosPtr: Long, kind: Int)

    external fun kScomSetWriterDataLifecycle(qosPtr: Long, autodispose: Boolean)

    external fun kScomSetReaderDataLifecycle(qosPtr: Long, autopurge_nowriter_samples_delay: Long, autopurge_disposed_samples_delay: Long)

    external fun kScomSetDurabilityservice(qosPtr: Long, service_cleanup_delay: Long, history_kind: Int, history_depth: Int, max_samples: Int, max_instances: Int, max_samples_per_instance: Int)

    external fun kScomSetIgnorelocal(qosPtr: Long, ignore : Int)

    external fun kScomSetTopicdata(qosPtr: Long, value :ByteArray, sz: Int)//todo

    external fun kScomSetUserdata(qosPtr: Long, value: ByteArray, sz: Int)

    external fun kScomSetGroupdata(qosPtr: Long, value: ByteArray, sz: Int)

    external fun kScomSetProperty(qosPtr: Long, name: String, value: String)

    external fun kScomSetBinaryproperty(qosPtr: Long, name: String, value:ByteArray, sz: Int)

    external fun kScomSetTypeconsistency(qosPtr: Long, kind: Int, ignore_sequence_bounds : Boolean, ignore_string_bounds: Boolean, ignore_member_names: Boolean, prevent_type_widening: Boolean, force_type_validation: Boolean)

    external fun kScomSetDataRepresentation(qosPtr: Long, kind: Int)

    external fun kScomSetEntityName(qosPtr: Long, name : String)

    external fun kScomGetReliability(qosPtr: Long) : Policy.Reliability

//    external fun kScomGetDurability(qosPtr: Long):Policy.Reliability

    external fun kScomGetDurability(qosPtr: Long): Policy.Durability

    external fun kScomGetHistory(qosPtr: Long): Policy.History

    external fun kScomGetResourceLimits(qosPtr: Long): Policy.ResourceLimits

    external fun kScomGetPresentation(qosPtr: Long):Policy.PresentationAccessScope

    external fun kScomGetLifespan(qosPtr: Long):Policy.Lifespan

    external fun kScomGetDeadline(qosPtr: Long):Policy.Deadline

    external fun kScomGetLatencyBudget(qosPtr: Long):Policy.LatencyBudget

    external fun kScomGetOwnership(qosPtr: Long):Policy.Ownership

    external fun kScomGetOwnershipStrength(qosPtr: Long):Policy.OwnershipStrength

    external fun kScomGetLiveliness(qosPtr: Long):Policy.Liveliness

    external fun kScomGetTimeBasedFilter(qosPtr: Long):Policy.TimeBasedFilter

    external fun kScomGetPartition(qosPtr: Long):Policy.Partition

    external fun kScomGetTransportPriority(qosPtr: Long):Policy.TransportPriority

    external fun kScomGetDestinationOrder(qosPtr: Long):Policy.DestinationOrder

    external fun kScomGetWriteDataLifecycle(qosPtr: Long):Policy.WriteDataLifecycle

    external fun kScomGetReaderDataLifecycle(qosPtr: Long):Policy.ReaderDataLifecycle

    external fun kScomGetDurabilityService(qosPtr: Long):Policy.DurabilityService

    external fun kScomGetIgnoreLocal(qosPtr: Long):Policy.IgnoreLocal

    external fun kScomGetUserdata(qosPtr: Long):Policy.Userdata

    external fun kScomGetTopicdata(qosPtr: Long):Policy.Topicdata

    external fun kScomGetGroupdata(qosPtr: Long):Policy.Groupdata

    external fun kScomGetProperty(qosPtr: Long):ArrayList<Policy.Property>

    external fun kScomGetBinaryProperty(qosPtr: Long):ArrayList<Policy.BinaryProperty>

    external fun kScomGetTypeConsistency(qosPtr: Long):Policy.TypeConsistency

    external fun kScomGetDataRepresentation(qosPtr: Long):Policy.DataRepresentation

    external fun kScomGetEntityName(qosPtr: Long):Policy.EntityName

}

