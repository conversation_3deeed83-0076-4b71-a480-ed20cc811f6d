package com.seres.dds.sdk.idl



class Buffer {

    private var m_bytes: ByteArray? = null
    private var m_pos: Int = 0
    private var m_size: Int = 0
    private var m_align_offset: Int = 0
    private var m_align_max: Int = 0
    private var m_endian: Int = 1      //1表示小端，2表示大端
    private var m_header:ByteArray = ByteArray(4)

    constructor(bytes: ByteArray? = null, align_offset: Int = 0, align_max: Int = 4){       //8

        if (bytes != null){
            m_bytes = bytes
            m_header = read_bytes(4)
        }else {
            m_bytes = ByteArray(1024)
            m_bytes!![0] = 0x00.toByte()
            m_bytes!![1] = 0x01.toByte()
            m_bytes!![2] = 0x00.toByte()
            m_bytes!![3] = 0x00.toByte()
            m_pos = 4
        }
        m_size = m_bytes!!.size
        m_align_offset = align_offset
        m_align_max = align_max
    }

    fun zero_out(){
        m_bytes = ByteArray(m_size)
    }

    fun set_align_offset(offset: Int){
        m_align_offset = offset
    }

    fun seek(pos: Int): Buffer{
        m_pos = pos
        return this
    }
        
    fun tell():Int {
        return m_pos
    }
        
    fun ensure_size(size: Int){
        if (m_pos + size > m_size){
            while (m_pos + size > m_size){
                m_size *= 2
            }
                
            m_bytes = m_bytes!!.copyOf(m_size)
        }
    }

    fun align(alignment: Int): Buffer {
        var alignmentmin = minOf(alignment, m_align_max)
        m_pos = ((m_pos - m_align_offset + alignmentmin - 1) and (alignmentmin - 1).inv()) + m_align_offset
        return this
    }
    public var last_align: Int = 0
    fun dealign(alignment:Int):Buffer{
        m_pos = m_pos - alignment
        return this
    }
        
    fun write_bytes(bytes: ByteArray): Buffer{
        val length = bytes.size
        ensure_size(length)
        bytes.copyInto(m_bytes!!, m_pos, 0, length)
        m_pos += length
        return this
    }

    fun read_bytes(length: Int): ByteArray{
        val b = m_bytes!!.sliceArray(m_pos .. m_pos + length -1)
        m_pos += length
        return b
    }

    fun get_bytes():ByteArray{
        return m_bytes!!.sliceArray(0 .. m_pos - 1)
    }

    fun get_pos():Int {
        return m_pos
    }
}

