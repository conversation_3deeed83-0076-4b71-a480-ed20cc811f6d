package com.seres.dds.sdk

import com.seres.dds.sdk.core.InstanceState
import com.seres.dds.sdk.core.SampleState
import com.seres.dds.sdk.core.ViewState
import com.seres.dds.sdk.idl.TypeBase

class SampleInfo {
    var valid_data: Boolean = false
    var sampleState: SampleState? = null
    var viewState: ViewState? = null
    var instanceState: InstanceState? = null
    var sourceTimestamp: Long = 0
    var instanceHandle: Int = 0
    var publicationHandle: Int = 0
    var disposedGenerationCount: Int = 0
    var noWritersGenerationCount: Int = 0
    var sampleRank: Int = 0
    var generationRank: Int = 0
    var absoluteGenerationRank: Int = 0
}

class SampleContainerT {
    var usample: ByteArray? = null // 指向样本数据的引用
    var usample_size: Long = 0 // usample的大小，使用Long以兼容64位平台
}

class Sample {
    var info: SampleInfo? = null
    var type: TypeBase = TypeBase()

    fun data(): TypeBase {
        return this.type
    }

    fun info(): SampleInfo? {
        return this.info
    }

    fun valid(): Boolean? {
        return this.info?.valid_data
    }
}

class Samples {
    var sample_list: MutableList<Sample>? = null
    var sample_count: Long = 0

    init {
        sample_list = mutableListOf<Sample>()
    }

    fun add(sample: Sample) {
        sample_list!!.add(sample)
        sample_count++
    }
}
