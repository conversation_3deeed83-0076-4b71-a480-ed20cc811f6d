package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.core.Entity
import com.seres.dds.sdk.idl.WaitSetAttachResp


class WaitSet : Entity {
    var attached = mutableMapOf<Entity, Long>()

    constructor(domain_participant: DomainParticipant) : super(
        KScomNativeLib.kScomCreateWaitSet(
            domain_participant.ref
        )
    ) {
        println("waitset par ref:" + domain_participant.ref)
    }

//    fun distory() {
//        for (key in attached.keys) {
//            _waitset_detach(super.ref, key.ref)
//        }
//    }

    fun attach(entity: Entity):Int {
        if (is_attached(entity) == true) {
            return -1
        }
//        var value_ptr = nativeHeap.alloc<IntVar>()

        val ret = _waitset_attach(super.ref, entity.ref)

        check(ret.ret >= 0) { "Occurred when trying to attach ${entity.ref} to ${super.ref}" }
        attached.put(entity, ret.valueptr)
        return ret.ret
    }

    fun detach(entity: Entity) {
        val entityToCheck = attached.containsKey(entity)
        if (!entityToCheck) {
            return
        }
        var value_ptr = attached[entity]!!
        val ret = _waitset_detach(super.ref, entity.ref, value_ptr)
        check(ret >= 0) { "Occurred when trying to detach ${entity.ref} to ${super.ref}" }
//        nativeHeap.free(value_pt)
        attached.remove(entity)
    }

    fun is_attached(entity: Entity): Boolean {

        val entityToCheck = attached.containsKey(entity)
        if (entityToCheck) {
            return true
        }
        return false
    }

    fun get_entities(): ArrayList<Entity> {
        var list: ArrayList<Entity> = ArrayList<Entity>()
        for (key in attached.keys) {
            list.add(key)
        }
        return list
    }

    fun wait(from: String, timeout: Long): Int {
        val ret = _waitset_wait(super.ref, 0UL, timeout)
        println(TAG + "from=$from, ref=${super.ref}, timeout=${timeout}, waitSet.wait ret = $ret")
        if (ret >= 0) {
            return ret
        }
        check(ret >= 0) { "from=$from, Occurred while waiting in ${super.ref}" }
        return ret
    }

    fun wait_until(abstime: Long): Int {
        var ret: Int = 0

            ret = _waitset_wait_until(ref, attached.size.toULong(), abstime)
        if (ret >= 0) {
            return ret
        }
        check(ret >= 0) { "Occurred while waiting in ${super.ref}" }
        return 0
    }

    fun set_trigger(vle: Boolean) {
        var ret = _waitset_set_trigger(super.ref, vle)
        check(ret >= 0) { "Occurred when setting trigger in  ${super.ref}" }
    }

    // fun wait_async(){

    // }
    fun _waitset_attach(waitset: Int, entity: Int): WaitSetAttachResp {
        return KScomNativeLib.kScomWaitSetAttach(waitset, entity)
    }

    fun _waitset_detach(waitset: Int, entityid: Int, valuePtr:Long): Int {
        return KScomNativeLib.kScomWaitSetDetach(waitset, entityid, valuePtr)
    }

    fun _waitset_wait(waitset: Int,nxs: ULong, reltimeout: Long): Int {
        return KScomNativeLib.kScomWaitSetWait(waitset,nxs.toLong(), reltimeout)
    }

    fun _waitset_wait_until(
        waitset: Int,
        nxs: ULong,
        abstimeout: Long
    ): Int {
        return KScomNativeLib.kScomWaitSetWaitUntil(waitset, nxs.toLong(), abstimeout)
    }

    fun _waitset_set_trigger(waitset: Int, trigger: Boolean): Int {
        return KScomNativeLib.kScomWaitSetSetTrigger(waitset, trigger)
    }

    companion object{
        private const val TAG = "WaitSet"
    }
}