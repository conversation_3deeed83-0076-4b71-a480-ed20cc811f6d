package com.seres.dds.sdk

import com.seres.dds.sdk.KScomNativeLib.kScomCreateListener
import com.seres.dds.sdk.KScomNativeLib.kScomLsetSubscriptionMatched
import com.seres.dds.sdk.KScomNativeLib.kScomLsetPublicationMatched
import com.seres.dds.sdk.KScomNativeLib.kScomLsetOnDataAvailable
import com.seres.dds.sdk.core.SubscriptionMatchedStatus
import com.seres.dds.sdk.core.PublicationMatchedStatus

import kotlin.reflect.*
import kotlin.reflect.full.*

open class Listener(kind:Int) {
    private var _entity_id: Long = 0
    private var _listenerKind: Int = 0

    init {
        _entity_id = kScomCreateListener()
        _listenerKind = kind

        val thisClass = this::class
        val superClass = this::class.supertypes.first().classifier as? KClass<*>
        val listenerclass = superClass!!.supertypes.first().classifier as? KClass<*>

        if (listenerclass != null){
            if (isOverride(thisClass, listenerclass, "on_publication_matched")){
                println("isOverride on_publication_matched")
                kScomLsetPublicationMatched(_entity_id, this, _listenerKind)
            }
            if (isOverride(thisClass, listenerclass, "on_subscription_matched")){
                println("isOverride on_subscription_matched")
                kScomLsetSubscriptionMatched(_entity_id, this, _listenerKind)
            }
            if (isOverride(thisClass, listenerclass, "on_data_available")){
                println("isOverride on_data_available")
                kScomLsetOnDataAvailable(_entity_id, this, _listenerKind)
            }
        }
    }

    private fun isOverride(thisClass: KClass<*>, superClass: KClass<*>, callbackName: String):Boolean{

        val thisMethod = thisClass.declaredMemberFunctions.find { it.name == callbackName }
        val superMethod = superClass.declaredMemberFunctions.find { it.name == callbackName }

        if (thisMethod!= null && superMethod!= null) {
            return thisMethod != superMethod
        }

        return false
    }

    fun getListenerRef():Long{
        return _entity_id
    }

    open fun on_publication_matched(reader:Int, status: PublicationMatchedStatus){

    }

    open fun on_subscription_matched(reader:Int, status: SubscriptionMatchedStatus){

    }

    open fun on_data_available(reader:Int){

    }
}