package com.seres.dds.sdk

// import android.util.Log
import com.seres.dds.sdk.KScomNativeLib.kScomCreateTopic
import com.seres.dds.sdk.core.Entity
import com.seres.dds.sdk.idl.TypeBase

class Topic : Entity {

    private var m_datatype: TypeBase? = null

    constructor(
        participant: DomainParticipant,
        topic_name: String,
        data_type: TypeBase
    ) : super(0) {

        m_datatype = data_type
        var desc = IdlTypeDescT()

        desc.typename = m_datatype!!.gettypename()

        desc.keyless = m_datatype!!.getkeyless()
        desc.version_support = m_datatype!!.getversion_support()
        desc.xt_type_data = null

        val ref = kScomCreateTopic(participant.ref, topic_name, desc, 0, 0)

        super.setref(ref)
    }

    fun get_dataType(): TypeBase {
        return m_datatype!!
    }
}

class IdlTypeDescT {
    var typename: String = ""
    var keyless: Boolean = false
    var version_support: Int = 1

    //    var xt_type_data : ByteArray
    var xt_type_data: IntArray? = null
}
