package com.seres.dds.sdk.idl

import kotlin.reflect.*
import kotlin.reflect.full.*

enum class PrimitiveType {
    Boolean,
    Byte,
    Short,
    Int,
    Long,
    UByte,
    UShort,
    UInt,
    ULong,
    Float,
    Double
}


fun Long.toByteArray(size: Int): ByteArray {
    val bytes = ByteArray(size)
    for (i in 0 until size) {
        bytes[i] = ((this shr (i * 8)) and 0xFF).toByte()
    }
    return bytes
}


fun ULong.toByteArray(size: Int): ByteArray {
    return toLong().toByteArray(size)
}


abstract class Machine {
    var name: String = ""
    var optional: Boolean = false
    var length: Int = 0
    constructor(inname: String, inoptional: Boolean, inlength:Int){ 
        name = inname
        optional = inoptional
        length = inlength
    }
    abstract fun serialize(buffer:Buffer, invalue:Any?)
    abstract fun deserialize(buffer:Buffer):Any?
    abstract fun default_initialize():Any
}


class PrimitiveMachine(inname: String, type: PrimitiveType, alignment: Int, inoptional: Boolean, inlength: Int):Machine(inname, inoptional, inlength){
    private val _type = type
    private val _align = alignment

    fun convertToByteArray(invalue: Any): ByteArray = when (invalue) {
        is Byte    -> byteArrayOf(invalue)
        is UByte   -> byteArrayOf(invalue.toByte())
        is Boolean -> byteArrayOf(if (invalue) 1 else 0)
        is Float   -> invalue.toBits().toLong().toByteArray(4)
        is Double  -> invalue.toBits().toLong().toByteArray(8)
        is Short   -> invalue.toLong().toByteArray(2)
        is UShort  -> invalue.toLong().toByteArray(2)
        is Int     -> invalue.toLong().toByteArray(4)
        is UInt    -> invalue.toLong().toByteArray(4)
        is Long    -> invalue.toByteArray(8)
        is ULong   -> invalue.toByteArray(8)
        else -> throw IllegalArgumentException("PrimitiveMachine Unsupported type")
    }

    fun convertFromArray(buffer: Buffer, primitiveType: PrimitiveType): Any {
        val bytes = when (primitiveType) {
            PrimitiveType.Byte    -> buffer.read_bytes(1)
            PrimitiveType.UByte   -> buffer.read_bytes(1)
            PrimitiveType.Boolean -> buffer.read_bytes(1)
            PrimitiveType.Float   -> buffer.read_bytes(4)
            PrimitiveType.Double  -> buffer.read_bytes(8)
            PrimitiveType.Short   -> buffer.read_bytes(2)
            PrimitiveType.UShort  -> buffer.read_bytes(2)
            PrimitiveType.Int     -> buffer.read_bytes(4)
            PrimitiveType.UInt    -> buffer.read_bytes(4)
            PrimitiveType.Long    -> buffer.read_bytes(8)
            PrimitiveType.ULong   -> buffer.read_bytes(8)
        }
    
        val result: Long = when (bytes.size) {
            1 -> (bytes[0].toLong() and 0xFF)
            2 -> (bytes[0].toLong() and 0xFF) or ((bytes[1].toLong() and 0xFF) shl 8)
            4 -> (bytes[0].toLong() and 0xFF) or ((bytes[1].toLong() and 0xFF) shl 8) or
                 ((bytes[2].toLong() and 0xFF) shl 16) or ((bytes[3].toLong() and 0xFF) shl 24)
            8 -> (bytes[0].toLong() and 0xFF) or ((bytes[1].toLong() and 0xFF) shl 8) or
                 ((bytes[2].toLong() and 0xFF) shl 16) or ((bytes[3].toLong() and 0xFF) shl 24) or
                 ((bytes[4].toLong() and 0xFF) shl 32) or ((bytes[5].toLong() and 0xFF) shl 40) or
                 ((bytes[6].toLong() and 0xFF) shl 48) or ((bytes[7].toLong() and 0xFF) shl 56)
            else -> throw IllegalArgumentException("PrimitiveMachine Unsupported byte size")
        }
    
        return when (primitiveType) {
            PrimitiveType.Boolean -> bytes[0] != 0.toByte()
            PrimitiveType.Byte    -> bytes[0]
            PrimitiveType.UByte   -> bytes[0].toUByte()
            PrimitiveType.Short   -> result.toShort()
            PrimitiveType.UShort  -> result.toUShort()
            PrimitiveType.Int     -> result.toInt()
            PrimitiveType.UInt    -> result.toUInt()
            PrimitiveType.Long    -> result
            PrimitiveType.ULong   -> result.toULong()
            PrimitiveType.Float   -> Float.fromBits(result.toInt())
            PrimitiveType.Double  -> Double.fromBits(result)
        }
    }

    override fun serialize(buffer:Buffer, invalue:Any?)
    {
        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }
        if (invalue == null){
            return
        }
        buffer.align(_align)
        val bytes = convertToByteArray(invalue)
        buffer.write_bytes(bytes)
    }

    override fun deserialize(buffer:Buffer):Any?{
        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }
        buffer.align(_align)
        return convertFromArray(buffer, _type)
    }
    override fun default_initialize():Any{
        return ByteArray(4) {0.toByte()}
    }
}


class StructMachine(inname: String, kclass: KClass<*>, inoptional: Boolean, inlength: Int):Machine(inname, inoptional, inlength){

    val serializeFun = kclass.memberFunctions.first { it.name == "serialize" }
    val deserializeFun = kclass.memberFunctions.first { it.name == "deserialize" }
    val m_instance = kclass.createInstance()

    override fun serialize(buffer:Buffer,invalue:Any?){
        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }

        if (invalue == null){
            return
        }

        serializeFun.call(invalue, buffer)
    }

    override fun deserialize(buffer:Buffer):Any?{
        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }

        return deserializeFun.call(m_instance, buffer)!!
    }

    override fun default_initialize():Any{
        return 0
    }
}


class UnionMachine(inname: String, kclass: KClass<*>, inoptional: Boolean, inlength: Int):Machine(inname, inoptional, inlength){

    val serializeFun = kclass.memberFunctions.first { it.name == "serialize" }
    val deserializeFun = kclass.memberFunctions.first { it.name == "deserialize" }
    val m_instance = kclass.createInstance()

    override fun serialize(buffer:Buffer,invalue:Any?){
        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }

        if (invalue == null){
            return
        }

        serializeFun.call(invalue, buffer)
    }

    override fun deserialize(buffer:Buffer):Any?{
        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }

        return deserializeFun.call(m_instance, buffer)!!
    }

    override fun default_initialize():Any{
        return 0
    }
}


class StringMachine(inname: String, inoptional: Boolean, inlength: Int) : Machine(inname, inoptional, inlength){ 

    var valueSize:UInt = 0U

    fun serializeHeader(buffer:Buffer){
        buffer.align(4)  

        var dataHeader = ByteArray(4)
        for(i in 0 ..3 ){
            dataHeader[i] = ((valueSize shr i*8) and 0xFFU).toByte()
        }
        buffer.write_bytes(dataHeader)
    }


    override fun serialize(buffer:Buffer,invalue:Any?){

        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }

        if (invalue == null){
            return
        }

        valueSize = if (invalue is String && invalue.isNotEmpty()) {
            invalue.length.toUInt() + 1u
        } else {
            0U
        }

        serializeHeader(buffer)

        if (valueSize == 0U){
            return
        }

        buffer.write_bytes(invalue.toString().encodeToByteArray())
        buffer.write_bytes(byteArrayOf(0))
    }


    override fun deserialize(buffer:Buffer):Any?{

        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }

        buffer.align(4)
        var bytes = buffer.read_bytes(4)
        
        var size = (bytes[0].toInt() and 0xFF) or
           ((bytes[1].toInt() and 0xFF) shl 8) or
           ((bytes[2].toInt() and 0xFF) shl 16) or
           ((bytes[3].toInt() and 0xFF) shl 24)

        val mbuffer = buffer.read_bytes(size - 1 )
        buffer.read_bytes(1)
        return mbuffer.decodeToString()
    }


    override fun default_initialize():Any{
        val charArray = CharArray(4) { _ -> '0' }
        val string = charArray.joinToString("")
        return string
    }
}


class ArrayListMachine(inname: String, val submachine:Machine, inoptional: Boolean, inlength: Int):Machine(inname, inoptional, inlength){

    // fun SerializeHeader(buffer:Buffer,size:Int=0){

    //     buffer.align(4)  

    //     var dataHeader = ByteArray(4)
    //     for(i in 0 ..3 ){
    //         dataHeader[i] = ((size shr i*8) and 0xFF).toByte()
    //     }
    //     buffer.write_bytes(dataHeader)
    // }

    override fun serialize(buffer:Buffer,invalue:Any?){

        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }

        if (invalue == null){
            return
        }

        if (invalue is ArrayList<*>){
            // SerializeHeader(buffer, invalue.size)
        }else{
            throw IllegalArgumentException("invalue is not ArrayList")
        }

        invalue.forEach{subdata ->
            submachine.serialize(buffer, subdata)
        }
    }

    override fun deserialize(buffer:Buffer):Any?{

        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }

        buffer.align(4)
        // var bytes = buffer.read_bytes(4)
        
        // var size = (bytes[0].toInt() and 0xFF) or
        //    ((bytes[1].toInt() and 0xFF) shl 8) or
        //    ((bytes[2].toInt() and 0xFF) shl 16) or
        //    ((bytes[3].toInt() and 0xFF) shl 24)
        
        var array = ArrayList<Any>()
        var size = length
        repeat(size){
            array.add(submachine.deserialize(buffer)!!)
        }

        return array
    }

    override fun default_initialize():Any{
        return 0
    }
}


class SequenceMachine(inname: String, val submachine:Machine, inoptional: Boolean, inlength: Int):Machine(inname, inoptional, inlength){

    fun SerializeHeader(buffer:Buffer,size:Int=0){

        buffer.align(4)

        var dataHeader = ByteArray(4)
        for(i in 0 ..3 ){
            dataHeader[i] = ((size shr i*8) and 0xFF).toByte()
        }
        buffer.write_bytes(dataHeader)
    }

    override fun serialize(buffer:Buffer,invalue:Any?){
        if (optional){
            buffer.align(1)
            buffer.write_bytes(byteArrayOf(if (invalue != null) 1 else 0))
        }

        if (invalue == null){
            return
        }

        if (invalue is ArrayDeque<*>){
            SerializeHeader(buffer, invalue.count())       //size
        }else{
            throw IllegalArgumentException("invalue is not ArrayDeque")
        }

        invalue.forEach{subdata ->
            submachine.serialize(buffer, subdata)
        }
    }

    override fun deserialize(buffer:Buffer):Any?{

        if (optional){
            buffer.align(1)
            val bytes = buffer.read_bytes(1)

            if (bytes[0] == 0.toByte()){
                return null
            }
        }

        buffer.align(4)
        var bytes = buffer.read_bytes(4)

        var size = (bytes[0].toInt() and 0xFF) or
                ((bytes[1].toInt() and 0xFF) shl 8) or
                ((bytes[2].toInt() and 0xFF) shl 16) or
                ((bytes[3].toInt() and 0xFF) shl 24)

        var array = ArrayDeque<Any>()
        repeat(size){
            array.add(submachine.deserialize(buffer)!!)
        }

        return array
    }

    override fun default_initialize():Any{
        return 0
    }
}