package com.seres.dds.sdk.idl

import android.util.Log
import kotlin.reflect.*
import kotlin.reflect.full.*
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.reflect


data class Member(val memberName: String, val optional: <PERSON><PERSON><PERSON>, var length: Int = 0, var kclass:KClass<*>? = null)


open class TypeBase{
    protected var keyless           = true
    protected var version_support   = 1
    protected var typename          = ""
    protected var buffer            = Buffer()
    protected var machines          = ArrayList<Machine>()
    protected var orderedMembers    = ArrayList<Member>()

    fun initproperty(){
        machines.clear()
        orderedMembers.forEach {member ->
            val property = this::class.memberProperties.find{it.name == member.memberName}?: throw NoSuchElementException("Property ${member.memberName} not found")
            machines.add(Builder.create_machine(member.kclass, property as KProperty1<*, *>, member.optional, member.length))
        }
    }

    fun updateporperty(name: String, ukclass: KClass<*>){
        orderedMembers.forEach{member ->
            if (member.memberName == name){
                member.kclass = ukclass
            }
            val property = this::class.memberProperties.find{it.name == member.memberName}?: throw NoSuchElementException("Property ${member.memberName} not found")
            machines.forEachIndexed { index, machine ->
                if (machine.name == member.memberName) {
                    machines[index] = Builder.create_machine(member.kclass, property as KProperty1<*, *>, member.optional, member.length)
                }
            }
        }
    }

    open fun serialize(inbuffer:Buffer){
        machines.forEach{ machine ->
            val property = this::class.memberProperties.find{it.name == machine.name}
            
            val value = try {
                property!!.getter.call(this)
            } catch (e: Exception) {
                throw IllegalArgumentException("Error: ${e.message}")
            }

            machine.serialize(inbuffer, value)
        }
    }

    open fun deserialize(inbuffer:Buffer): Any?{
        val constructor = this::class.constructors.first()
        val instance = constructor.call()

        machines.forEach{ machine ->
            
            val property = this::class.memberProperties.find{it.name == machine.name}
            val result = machine.deserialize(inbuffer)

            property?.let {
                if (it is KMutableProperty1<*, *>) {
                    @Suppress("UNCHECKED_CAST")
                    (it as KMutableProperty1<Any, Any>).setter.call(instance, result)
                } else {
                    throw IllegalArgumentException("Property ${machine.name} is read-only and cannot be set.")
                }
            } ?: throw IllegalArgumentException("Property ${machine.name} not found.")
        }

        return instance
    }

    fun getbuffer(): Buffer {
        return buffer
    }

    fun gettypename(): String {
        return typename
    }

    fun getkeyless(): Boolean {
        return keyless
    }

    fun getversion_support(): Int{
        return version_support
    }
}


open class TypeStruct: TypeBase()

open class TypeUnion : TypeBase(){
    var ukclass : KClass<*> = this::class
    var dmutableMap: MutableMap<Any, KClass<*>> = mutableMapOf()
    override fun deserialize(inbuffer: Buffer):Any?{


        val type_d = ukclass.memberProperties.find { it.name == "__d" }
        if(null == type_d){
            throw IllegalArgumentException("derialize _d error")
        }else{
            println("$type_d")
        }

        val machine_d: Any
        val result_d : Any
        if(type_d.returnType.classifier == Int::class){
            machine_d = PrimitiveMachine("__d", PrimitiveType.Int,4,false,0)
            result_d = machine_d.deserialize(inbuffer)!!
        }else if(type_d.returnType.classifier == Long::class){
            machine_d = PrimitiveMachine("__d", PrimitiveType.Long,8,false,0)
            result_d = machine_d.deserialize(inbuffer)!!
        }else{
            throw IllegalArgumentException("derialize result_d error ${type_d.returnType}")
        }

        val subkclass = dmutableMap[result_d]
        if(subkclass == null){
            throw IllegalArgumentException("derialize _d error")
        }

        val subconstructor = subkclass.constructors.first()
        val subinstance = subconstructor.call()

        val propertyd = subkclass.memberProperties.find{it.name == "__d"}
        propertyd?.let {
            if (it is KMutableProperty1<*, *>) {
                @Suppress("UNCHECKED_CAST")
                (it as KMutableProperty1<Any, Any>).setter.call(subinstance, result_d)
            } else {
                throw IllegalArgumentException("Property __d is read-only and cannot be set.")
            }
        } ?: throw IllegalArgumentException("Property __d not found.")

        val property_machines = subkclass.memberProperties.find { it.name == "machines" }
        property_machines?.isAccessible = true

        var sub_machines : ArrayList<Machine> = (subinstance as TypeUnion).machines
        sub_machines.removeAt(0)

        val machine = sub_machines.get(0)
        val property = subkclass.memberProperties.find{it.name == machine.name}
        val result = machine.deserialize(inbuffer)
        property?.isAccessible = true
        property?.let {
            if (it is KMutableProperty1<*, *>) {
                @Suppress("UNCHECKED_CAST")
                (it as KMutableProperty1<Any, Any>).setter.call(subinstance, result)
            } else {
                throw IllegalArgumentException("Property ${machine.name} is read-only and cannot be set.")
            }
        } ?: throw IllegalArgumentException("Property ${machine.name} not found.")

        val property2 =  subkclass.memberProperties.find{it.name == "__u"}
        property2?.isAccessible = true
        property2?.let {
            if (it is KMutableProperty1<*, *>) {
                @Suppress("UNCHECKED_CAST")
                (it as KMutableProperty1<Any, Any>).setter.call(subinstance, subinstance)
            } else {
                throw IllegalArgumentException("Property2 __u is read-only and cannot be set.")
            }
        } ?: throw IllegalArgumentException("Property2 __u not found.")

        return subinstance
    }
}